/**
 * 登录路由调试脚本
 * 用于诊断前端无法跳转到登录页面的问题
 */

// 调试登录路由问题
function debugLoginRouting() {
  console.log('🔍 开始调试登录路由问题...')
  
  const results = {
    routerExists: false,
    loginRouteExists: false,
    storeExists: false,
    authStateCorrect: false,
    navigationWorks: false,
    overall: false
  }
  
  try {
    // 1. 检查路由器是否存在
    console.log('1️⃣ 检查路由器状态...')
    
    if (window.$router || (window.app && window.app.config.globalProperties.$router)) {
      results.routerExists = true
      console.log('✅ Vue Router 已加载')
      
      const router = window.$router || window.app.config.globalProperties.$router
      console.log('📋 当前路由:', router.currentRoute.value.path)
      console.log('📋 路由历史:', router.getRoutes().map(r => r.path))
    } else {
      console.log('❌ Vue Router 未找到')
    }
    
    // 2. 检查登录路由是否存在
    console.log('2️⃣ 检查登录路由配置...')
    
    const router = window.$router || (window.app && window.app.config.globalProperties.$router)
    if (router) {
      const loginRoute = router.getRoutes().find(route => route.path === '/login')
      if (loginRoute) {
        results.loginRouteExists = true
        console.log('✅ 登录路由已配置')
        console.log('📋 登录路由信息:', {
          path: loginRoute.path,
          name: loginRoute.name,
          meta: loginRoute.meta
        })
      } else {
        console.log('❌ 登录路由未找到')
        console.log('📋 可用路由:', router.getRoutes().map(r => ({ path: r.path, name: r.name })))
      }
    }
    
    // 3. 检查 Vuex Store 状态
    console.log('3️⃣ 检查 Vuex Store 状态...')
    
    if (window.$store || (window.app && window.app.config.globalProperties.$store)) {
      results.storeExists = true
      console.log('✅ Vuex Store 已加载')
      
      const store = window.$store || window.app.config.globalProperties.$store
      
      // 检查认证状态
      try {
        const isAuthenticated = store.getters['auth/isAuthenticated']
        const currentUser = store.getters['auth/currentUser']
        const token = store.state.auth.token
        
        console.log('📋 认证状态:', {
          isAuthenticated,
          hasUser: !!currentUser,
          hasToken: !!token,
          userRole: currentUser?.role
        })
        
        // 检查认证状态是否正确
        if (typeof isAuthenticated === 'boolean') {
          results.authStateCorrect = true
          console.log('✅ 认证状态正常')
        } else {
          console.log('❌ 认证状态异常')
        }
        
      } catch (error) {
        console.log('❌ 获取认证状态失败:', error.message)
      }
    } else {
      console.log('❌ Vuex Store 未找到')
    }
    
    // 4. 测试路由导航
    console.log('4️⃣ 测试路由导航...')
    
    const router = window.$router || (window.app && window.app.config.globalProperties.$router)
    if (router) {
      try {
        // 记录当前路径
        const currentPath = router.currentRoute.value.path
        console.log('📋 当前路径:', currentPath)
        
        // 尝试导航到登录页面
        router.push('/login').then(() => {
          console.log('✅ 路由导航成功')
          results.navigationWorks = true
          
          // 检查是否真的跳转了
          setTimeout(() => {
            const newPath = router.currentRoute.value.path
            console.log('📋 导航后路径:', newPath)
            
            if (newPath === '/login') {
              console.log('✅ 成功跳转到登录页面')
            } else {
              console.log('❌ 跳转失败，仍在:', newPath)
            }
          }, 100)
          
        }).catch(error => {
          console.log('❌ 路由导航失败:', error.message)
        })
        
      } catch (error) {
        console.log('❌ 路由导航测试失败:', error.message)
      }
    }
    
    // 5. 检查页面元素
    console.log('5️⃣ 检查页面元素...')
    
    // 检查登录链接
    const loginLinks = document.querySelectorAll('a[href="/login"], a[href="#/login"]')
    console.log('📋 找到登录链接数量:', loginLinks.length)
    
    if (loginLinks.length > 0) {
      loginLinks.forEach((link, index) => {
        console.log(`📋 登录链接 ${index + 1}:`, {
          href: link.href,
          text: link.textContent.trim(),
          visible: link.offsetParent !== null
        })
      })
    }
    
    // 检查路由链接
    const routerLinks = document.querySelectorAll('[to="/login"]')
    console.log('📋 找到 router-link 数量:', routerLinks.length)
    
    // 6. 综合评估
    const passedTests = Object.values(results).filter(v => v === true).length
    const totalTests = Object.keys(results).length - 1 // 排除 overall
    results.overall = passedTests >= totalTests * 0.8 // 80% 通过率
    
    console.log('\n📊 登录路由调试结果:')
    console.log('=' .repeat(50))
    console.log(`路由器状态: ${results.routerExists ? '✅ 正常' : '❌ 异常'}`)
    console.log(`登录路由: ${results.loginRouteExists ? '✅ 存在' : '❌ 缺失'}`)
    console.log(`Store状态: ${results.storeExists ? '✅ 正常' : '❌ 异常'}`)
    console.log(`认证状态: ${results.authStateCorrect ? '✅ 正常' : '❌ 异常'}`)
    console.log(`路由导航: ${results.navigationWorks ? '✅ 正常' : '❌ 异常'}`)
    console.log('=' .repeat(50))
    console.log(`总体结果: ${results.overall ? '✅ 基本正常' : '❌ 存在问题'} (${passedTests}/${totalTests})`)
    
    if (results.overall) {
      console.log('\n🎉 路由系统基本正常！')
      console.log('💡 如果仍无法跳转，请检查:')
      console.log('  - 浏览器控制台是否有JavaScript错误')
      console.log('  - 网络请求是否正常')
      console.log('  - 页面是否正确加载')
    } else {
      console.log('\n⚠️ 发现路由问题，请检查:')
      if (!results.routerExists) console.log('  - Vue Router 配置和初始化')
      if (!results.loginRouteExists) console.log('  - 登录路由配置')
      if (!results.storeExists) console.log('  - Vuex Store 配置')
      if (!results.authStateCorrect) console.log('  - 认证状态管理')
      if (!results.navigationWorks) console.log('  - 路由导航功能')
    }
    
    return results
    
  } catch (error) {
    console.error('❌ 调试过程中发生错误:', error)
    return results
  }
}

// 检查控制台错误
function checkConsoleErrors() {
  console.log('\n🔍 检查控制台错误...')
  
  // 监听错误
  const originalError = console.error
  const errors = []
  
  console.error = function(...args) {
    errors.push(args.join(' '))
    originalError.apply(console, args)
  }
  
  // 检查现有错误
  if (window.console && window.console.memory) {
    console.log('📋 浏览器控制台可用')
  }
  
  // 恢复原始 console.error
  setTimeout(() => {
    console.error = originalError
    
    if (errors.length > 0) {
      console.log('❌ 检测到控制台错误:')
      errors.forEach((error, index) => {
        console.log(`  ${index + 1}. ${error}`)
      })
    } else {
      console.log('✅ 没有检测到控制台错误')
    }
  }, 1000)
}

// 手动测试登录跳转
function testLoginNavigation() {
  console.log('\n🧪 手动测试登录跳转...')
  
  try {
    // 方法1: 使用 router.push
    if (window.$router) {
      console.log('📋 尝试使用 router.push...')
      window.$router.push('/login')
        .then(() => console.log('✅ router.push 成功'))
        .catch(err => console.log('❌ router.push 失败:', err.message))
    }
    
    // 方法2: 使用 window.location
    setTimeout(() => {
      console.log('📋 尝试使用 window.location...')
      // window.location.href = '/login'  // 注释掉避免实际跳转
      console.log('💡 可以尝试: window.location.href = "/login"')
    }, 1000)
    
    // 方法3: 模拟点击登录链接
    setTimeout(() => {
      console.log('📋 尝试模拟点击登录链接...')
      const loginLink = document.querySelector('a[href="/login"]')
      if (loginLink) {
        console.log('✅ 找到登录链接，可以尝试点击')
        // loginLink.click()  // 注释掉避免实际跳转
        console.log('💡 可以尝试: document.querySelector(\'a[href="/login"]\').click()')
      } else {
        console.log('❌ 未找到登录链接')
      }
    }, 1500)
    
  } catch (error) {
    console.error('❌ 测试登录跳转失败:', error)
  }
}

// 检查页面加载状态
function checkPageLoadStatus() {
  console.log('\n📄 检查页面加载状态...')
  
  console.log('📋 页面状态:', {
    readyState: document.readyState,
    title: document.title,
    url: window.location.href,
    hash: window.location.hash,
    pathname: window.location.pathname
  })
  
  // 检查 Vue 应用是否已挂载
  const appElement = document.getElementById('app')
  if (appElement) {
    console.log('✅ Vue 应用容器存在')
    console.log('📋 应用内容长度:', appElement.innerHTML.length)
    
    if (appElement.innerHTML.length > 100) {
      console.log('✅ Vue 应用已渲染内容')
    } else {
      console.log('❌ Vue 应用可能未正确渲染')
    }
  } else {
    console.log('❌ Vue 应用容器不存在')
  }
  
  // 检查路由视图
  const routerView = document.querySelector('router-view')
  if (routerView) {
    console.log('✅ 路由视图存在')
  } else {
    console.log('❌ 路由视图不存在')
  }
}

// 导出调试函数到全局
window.debugLoginRouting = debugLoginRouting
window.checkConsoleErrors = checkConsoleErrors
window.testLoginNavigation = testLoginNavigation
window.checkPageLoadStatus = checkPageLoadStatus

console.log('🔍 登录路由调试工具已加载！')
console.log('📋 可用命令:')
console.log('  window.debugLoginRouting()    - 完整路由调试')
console.log('  window.checkConsoleErrors()   - 检查控制台错误')
console.log('  window.testLoginNavigation()  - 测试登录跳转')
console.log('  window.checkPageLoadStatus()  - 检查页面加载状态')
console.log('')
console.log('🚀 快速开始: window.debugLoginRouting()')

// 自动运行基础检查
setTimeout(() => {
  console.log('\n🔍 自动运行基础检查...')
  
  // 检查当前路径
  console.log('📋 当前路径:', window.location.pathname)
  
  // 检查是否在登录页面
  if (window.location.pathname === '/login') {
    console.log('✅ 当前已在登录页面')
  } else {
    console.log('📋 当前不在登录页面')
  }
  
  // 检查登录链接是否可见
  const loginLink = document.querySelector('a[href="/login"]')
  if (loginLink && loginLink.offsetParent !== null) {
    console.log('✅ 登录链接可见')
  } else {
    console.log('⚠️ 登录链接不可见或不存在')
  }
  
  // 检查是否有JavaScript错误
  if (window.console && window.console.error) {
    console.log('✅ 控制台功能正常')
  }
  
}, 1000)
