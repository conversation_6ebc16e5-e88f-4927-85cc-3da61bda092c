/**
 * 前后端集成测试脚本
 * 验证前端是否正确调用后端接口，不再使用mock数据
 */

// 测试前后端集成
async function testIntegration() {
  console.log('🔗 开始测试前后端集成...')
  
  const results = {
    mockRemoved: false,
    apiCalls: false,
    dataConsistency: false,
    authentication: false,
    overall: false
  }
  
  try {
    // 1. 验证Mock数据已移除
    console.log('1️⃣ 验证Mock数据已移除...')
    const mockFunctions = [
      'getUserList',
      'getUserDetail', 
      'createUser',
      'updateUser',
      'deleteUser',
      'batchDeleteUsers',
      'toggleUserStatus',
      'batchToggleUserStatus',
      'resetUserPassword',
      'getUserStatistics',
      'exportUsers'
    ]
    
    const existingMockFunctions = mockFunctions.filter(fn => typeof window[fn] === 'function')
    
    if (existingMockFunctions.length === 0) {
      results.mockRemoved = true
      console.log('✅ Mock数据已完全移除')
    } else {
      console.log('❌ 仍存在Mock函数:', existingMockFunctions)
    }
    
    // 2. 测试API调用
    console.log('2️⃣ 测试API调用...')
    
    // 检查是否有userAPI模块
    if (typeof window.userAPI !== 'undefined') {
      console.log('✅ userAPI模块可用')
      results.apiCalls = true
    } else {
      console.log('⚠️ userAPI模块不可用，尝试直接测试...')
      
      // 尝试直接调用API
      try {
        const response = await fetch('/api/admin/users?current=1&size=5', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
          }
        })
        
        if (response.ok) {
          const data = await response.json()
          console.log('✅ 直接API调用成功，返回数据:', data)
          results.apiCalls = true
        } else {
          console.log('❌ API调用失败，状态码:', response.status)
        }
      } catch (error) {
        console.log('❌ API调用异常:', error.message)
      }
    }
    
    // 3. 测试数据一致性
    console.log('3️⃣ 测试数据一致性...')
    
    try {
      // 尝试获取用户列表
      const response = await fetch('/api/admin/users?current=1&size=3', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
        }
      })
      
      if (response.ok) {
        const data = await response.json()
        
        // 检查数据结构
        if (data.code === 200 && data.data && Array.isArray(data.data.records)) {
          console.log('✅ 数据结构正确')
          console.log('📊 用户数量:', data.data.total)
          console.log('📋 示例用户:', data.data.records[0])
          results.dataConsistency = true
        } else {
          console.log('❌ 数据结构不正确:', data)
        }
      } else if (response.status === 401) {
        console.log('⚠️ 需要登录认证')
      } else {
        console.log('❌ 数据获取失败，状态码:', response.status)
      }
    } catch (error) {
      console.log('❌ 数据一致性测试异常:', error.message)
    }
    
    // 4. 测试认证状态
    console.log('4️⃣ 测试认证状态...')
    
    const token = localStorage.getItem('token')
    const user = JSON.parse(localStorage.getItem('user') || '{}')
    
    if (token && user.role === 'ADMIN') {
      console.log('✅ 管理员认证有效')
      console.log('👤 当前用户:', user.username)
      results.authentication = true
    } else if (token) {
      console.log('⚠️ 已登录但非管理员用户')
    } else {
      console.log('⚠️ 未登录，请先登录管理员账户')
    }
    
    // 5. 综合评估
    const passedTests = Object.values(results).filter(v => v === true).length
    const totalTests = Object.keys(results).length - 1 // 排除overall
    results.overall = passedTests >= totalTests * 0.75 // 75%通过率
    
    console.log('\n📊 集成测试结果汇总:')
    console.log('=' .repeat(50))
    console.log(`Mock数据移除: ${results.mockRemoved ? '✅ 通过' : '❌ 失败'}`)
    console.log(`API调用测试: ${results.apiCalls ? '✅ 通过' : '❌ 失败'}`)
    console.log(`数据一致性: ${results.dataConsistency ? '✅ 通过' : '❌ 失败'}`)
    console.log(`认证状态: ${results.authentication ? '✅ 通过' : '❌ 失败'}`)
    console.log('=' .repeat(50))
    console.log(`总体结果: ${results.overall ? '✅ 集成成功' : '❌ 存在问题'} (${passedTests}/${totalTests})`)
    
    if (results.overall) {
      console.log('\n🎉 恭喜！前后端集成测试通过！')
      console.log('📋 集成特性:')
      console.log('  ✅ 前端不再使用Mock数据')
      console.log('  ✅ 直接调用后端真实接口')
      console.log('  ✅ 数据结构完全一致')
      console.log('  ✅ 认证和权限正常工作')
    } else {
      console.log('\n⚠️ 集成存在问题，请检查:')
      if (!results.mockRemoved) console.log('  - 清理残留的Mock函数')
      if (!results.apiCalls) console.log('  - 检查API调用配置')
      if (!results.dataConsistency) console.log('  - 验证数据结构一致性')
      if (!results.authentication) console.log('  - 确认认证状态')
    }
    
    return results
    
  } catch (error) {
    console.error('❌ 集成测试过程中发生错误:', error)
    return results
  }
}

// 测试后端服务状态
async function testBackendStatus() {
  console.log('\n🔧 测试后端服务状态...')
  
  try {
    // 测试健康检查接口
    const healthResponse = await fetch('/actuator/health')
    if (healthResponse.ok) {
      const health = await healthResponse.json()
      console.log('✅ 后端服务健康状态:', health.status)
    } else {
      console.log('❌ 健康检查失败')
    }
    
    // 测试API基础路径
    const apiResponse = await fetch('/api/admin/users/statistics', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
      }
    })
    
    if (apiResponse.ok) {
      console.log('✅ API接口可访问')
    } else if (apiResponse.status === 401) {
      console.log('⚠️ API需要认证（正常）')
    } else {
      console.log('❌ API接口异常，状态码:', apiResponse.status)
    }
    
  } catch (error) {
    console.error('❌ 后端服务测试失败:', error.message)
    console.log('💡 请确保后端服务已启动：mvn spring-boot:run')
  }
}

// 快速修复指南
function showFixGuide() {
  console.log('\n🔧 快速修复指南:')
  console.log('')
  console.log('1. 如果Mock函数仍存在:')
  console.log('   - 检查 frontend/src/api/admin/users.js')
  console.log('   - 确保没有 window.xxx = function() 的代码')
  console.log('   - 清理浏览器缓存并刷新页面')
  console.log('')
  console.log('2. 如果API调用失败:')
  console.log('   - 确保后端服务已启动: mvn spring-boot:run')
  console.log('   - 检查数据库连接配置')
  console.log('   - 验证API路径是否正确')
  console.log('')
  console.log('3. 如果认证失败:')
  console.log('   - 重新登录管理员账户')
  console.log('   - 用户名: admin')
  console.log('   - 密码: admin123')
  console.log('')
  console.log('4. 如果数据结构不匹配:')
  console.log('   - 检查后端DTO类定义')
  console.log('   - 验证数据库表结构')
  console.log('   - 运行数据库迁移脚本')
}

// 导出测试函数到全局
window.testIntegration = testIntegration
window.testBackendStatus = testBackendStatus
window.showFixGuide = showFixGuide

console.log('🔗 前后端集成测试工具已加载！')
console.log('📋 可用命令:')
console.log('  window.testIntegration()   - 运行完整集成测试')
console.log('  window.testBackendStatus() - 测试后端服务状态')
console.log('  window.showFixGuide()      - 显示修复指南')
console.log('')
console.log('🚀 快速开始: window.testIntegration()')

// 自动运行基础检查
setTimeout(() => {
  console.log('\n🔍 自动运行基础检查...')
  
  // 检查Mock函数
  const mockExists = typeof window.getUserList === 'function'
  if (mockExists) {
    console.log('⚠️ 检测到Mock函数仍然存在，建议运行完整测试')
  } else {
    console.log('✅ Mock函数已清理')
  }
  
  // 检查认证状态
  const token = localStorage.getItem('token')
  if (token) {
    console.log('✅ 检测到认证Token')
  } else {
    console.log('⚠️ 未检测到认证Token，请先登录')
  }
  
}, 1000)
