version: '3.8'

services:
  # MySQL 数据库
  mysql:
    image: mysql:8.0
    container_name: code-combined-mysql-prod
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD:-your-strong-password}
      MYSQL_DATABASE: code_combined
      MYSQL_USER: codecombined
      MYSQL_PASSWORD: ${DB_PASSWORD:-your-db-password}
    ports:
      - "3306:3306"
    volumes:
      - mysql_prod_data:/var/lib/mysql
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
      - ./mysql/conf.d:/etc/mysql/conf.d
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - code-combined-prod-network

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: code-combined-redis-prod
    restart: always
    command: redis-server --requirepass ${REDIS_PASSWORD:-your-redis-password}
    ports:
      - "6379:6379"
    volumes:
      - redis_prod_data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - code-combined-prod-network

  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    container_name: code-combined-backend-prod
    restart: always
    ports:
      - "8080:8080"
    environment:
      SPRING_PROFILES_ACTIVE: prod
      SPRING_DATASOURCE_URL: **********************************************************************************************************************
      SPRING_DATASOURCE_USERNAME: codecombined
      SPRING_DATASOURCE_PASSWORD: ${DB_PASSWORD:-your-db-password}
      SPRING_REDIS_HOST: redis
      SPRING_REDIS_PORT: 6379
      SPRING_REDIS_PASSWORD: ${REDIS_PASSWORD:-your-redis-password}
      JWT_SECRET: ${JWT_SECRET:-your-jwt-secret-key}
      MAIL_USERNAME: ${MAIL_USERNAME:-<EMAIL>}
      MAIL_PASSWORD: ${MAIL_PASSWORD:-your-email-password}
    depends_on:
      - mysql
      - redis
    volumes:
      - ./logs:/app/logs
    networks:
      - code-combined-prod-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.prod
    container_name: code-combined-frontend-prod
    restart: always
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - backend
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./logs/nginx:/var/log/nginx
    networks:
      - code-combined-prod-network

  # Nginx 负载均衡器（可选）
  nginx-lb:
    image: nginx:alpine
    container_name: code-combined-nginx-lb
    restart: always
    ports:
      - "8081:80"
    volumes:
      - ./nginx/lb.conf:/etc/nginx/nginx.conf
    depends_on:
      - frontend
    networks:
      - code-combined-prod-network

volumes:
  mysql_prod_data:
  redis_prod_data:

networks:
  code-combined-prod-network:
    driver: bridge
