#!/bin/bash

# Code-Combined 项目启动脚本
echo "==================================="
echo "Code-Combined 项目启动脚本"
echo "==================================="

# 检查环境
check_requirements() {
    echo "检查环境要求..."
    
    # 检查Java
    if ! command -v java &> /dev/null; then
        echo "❌ Java 未安装，请安装 Java 11+"
        exit 1
    fi
    
    # 检查Maven
    if ! command -v mvn &> /dev/null; then
        echo "❌ Maven 未安装，请安装 Maven 3.6+"
        exit 1
    fi
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        echo "❌ Node.js 未安装，请安装 Node.js 16+"
        exit 1
    fi
    
    # 检查npm
    if ! command -v npm &> /dev/null; then
        echo "❌ npm 未安装，请安装 npm"
        exit 1
    fi
    
    echo "✅ 环境检查通过"
}

# 启动数据库
start_database() {
    echo "启动数据库..."
    echo "请确保 MySQL 和 Redis 已启动"
    echo "MySQL 连接信息: localhost:3306"
    echo "Redis 连接信息: localhost:6379"
    
    # 检查MySQL连接
    if ! mysql -u root -p123456 -e "SELECT 1;" &> /dev/null; then
        echo "⚠️  无法连接到 MySQL，请检查数据库配置"
        echo "请执行以下命令初始化数据库:"
        echo "mysql -u root -p < database/init.sql"
    else
        echo "✅ MySQL 连接正常"
    fi
}

# 启动后端
start_backend() {
    echo "启动后端服务..."
    cd backend
    
    echo "安装依赖..."
    mvn clean install -DskipTests
    
    echo "启动 Spring Boot 应用..."
    mvn spring-boot:run &
    BACKEND_PID=$!
    
    echo "后端服务启动中，PID: $BACKEND_PID"
    echo "等待后端服务启动完成..."
    sleep 30
    
    cd ..
}

# 启动前端
start_frontend() {
    echo "启动前端服务..."
    cd frontend
    
    echo "安装依赖..."
    npm install
    
    echo "启动 Vue 开发服务器..."
    npm run dev &
    FRONTEND_PID=$!
    
    echo "前端服务启动中，PID: $FRONTEND_PID"
    
    cd ..
}

# 显示启动信息
show_info() {
    echo "==================================="
    echo "🎉 Code-Combined 启动完成！"
    echo "==================================="
    echo "前端地址: http://localhost:3000"
    echo "后端地址: http://localhost:8080"
    echo "API文档: http://localhost:8080/api/swagger-ui.html"
    echo "==================================="
    echo "按 Ctrl+C 停止所有服务"
    echo "==================================="
}

# 清理函数
cleanup() {
    echo "正在停止服务..."
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null
        echo "后端服务已停止"
    fi
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null
        echo "前端服务已停止"
    fi
    exit 0
}

# 设置信号处理
trap cleanup SIGINT SIGTERM

# 主函数
main() {
    check_requirements
    start_database
    start_backend
    start_frontend
    show_info
    
    # 等待用户中断
    wait
}

# 执行主函数
main
