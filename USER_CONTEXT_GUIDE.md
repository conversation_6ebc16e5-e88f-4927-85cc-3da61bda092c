# UserContext 用户上下文系统指南

## 🎯 概述

已创建完整的用户上下文系统，包括 `UserContext`、`JwtUtil`、`AuthInterceptor` 和 `RoleInterceptor`，为项目提供了完整的用户认证和权限控制功能。

## 📁 创建的组件

### 1. UserContext.java ✅
**功能**：线程本地用户上下文管理
**位置**：`com.codecombined.util.UserContext`

**核心特性**：
- ThreadLocal 存储用户信息
- 完整的用户信息封装
- 权限检查方法
- 异步操作支持
- 请求时长统计

### 2. JwtUtil.java ✅
**功能**：JWT Token 生成和解析
**位置**：`com.codecombined.util.JwtUtil`

**核心特性**：
- Token 生成和验证
- 用户信息提取
- Token 刷新机制
- 过期时间检查
- 安全密钥管理

### 3. AuthInterceptor.java ✅
**功能**：JWT 认证拦截器
**位置**：`com.codecombined.interceptor.AuthInterceptor`

**核心特性**：
- Token 自动解析
- 用户上下文设置
- 公开路径跳过
- 自动 Token 刷新
- 请求日志记录

### 4. RoleInterceptor.java ✅
**功能**：角色权限拦截器
**位置**：`com.codecombined.interceptor.RoleInterceptor`

**核心特性**：
- 角色权限检查
- 注解驱动权限控制
- 权限等级支持
- 错误响应处理

### 5. WebConfig.java ✅
**功能**：Web 配置类（已更新）
**位置**：`com.codecombined.config.WebConfig`

**核心特性**：
- 拦截器注册
- CORS 跨域配置
- 路径排除规则
- 执行顺序控制

## 🔧 系统架构

### 1. 认证流程
```
请求 → AuthInterceptor → JWT验证 → UserContext设置 → RoleInterceptor → 权限检查 → 业务逻辑
```

### 2. 拦截器执行顺序
1. **AuthInterceptor** (order=1) - JWT认证和用户上下文设置
2. **RoleInterceptor** (order=2) - 角色权限检查

### 3. 用户上下文生命周期
```
请求开始 → Token解析 → UserContext设置 → 业务处理 → 请求结束 → UserContext清理
```

## 🚀 使用指南

### 1. 在控制器中使用

**获取当前用户信息**：
```java
@RestController
@RequestMapping("/api/user")
public class UserController {
    
    @GetMapping("/profile")
    public Result<UserProfile> getProfile() {
        // 获取当前用户ID
        Long userId = UserContext.getCurrentUserId();
        
        // 获取当前用户信息
        UserContext.UserInfo userInfo = UserContext.getCurrentUser();
        
        // 检查用户是否登录
        if (!UserContext.isLoggedIn()) {
            return Result.error("用户未登录");
        }
        
        // 业务逻辑
        UserProfile profile = userService.getProfile(userId);
        return Result.success(profile);
    }
}
```

**权限检查**：
```java
@RestController
@RequestMapping("/api/admin")
@RequireRole(UserRole.ADMIN)  // 类级别权限控制
public class AdminController {
    
    @GetMapping("/users")
    public Result<List<User>> getUsers() {
        // 方法会自动进行管理员权限检查
        List<User> users = userService.getAllUsers();
        return Result.success(users);
    }
    
    @PostMapping("/users/{userId}/reset-password")
    @RequireRole(UserRole.ADMIN)  // 方法级别权限控制
    public Result<Void> resetPassword(@PathVariable Long userId) {
        // 额外的权限检查
        if (!UserContext.canAccessUser(userId)) {
            return Result.error("无权限操作该用户");
        }
        
        userService.resetPassword(userId);
        return Result.success();
    }
}
```

### 2. 在服务层中使用

**获取当前用户**：
```java
@Service
public class UserServiceImpl implements UserService {
    
    @Override
    public void updateProfile(UserUpdateRequest request) {
        // 获取当前用户ID
        Long currentUserId = UserContext.getCurrentUserId();
        
        // 权限检查
        if (!UserContext.canAccessUser(request.getUserId())) {
            throw new BusinessException("无权限修改该用户信息");
        }
        
        // 业务逻辑
        User user = getById(request.getUserId());
        // ... 更新逻辑
    }
    
    @Override
    public void createProblem(ProblemCreateRequest request) {
        // 获取当前用户信息
        UserContext.UserInfo userInfo = UserContext.getCurrentUser();
        
        Problem problem = new Problem();
        problem.setTitle(request.getTitle());
        problem.setCreatorId(userInfo.getUserId());
        problem.setCreatorName(userInfo.getUsername());
        
        save(problem);
    }
}
```

**权限检查方法**：
```java
@Service
public class ProblemServiceImpl implements ProblemService {
    
    @Override
    public void deleteProblem(Long problemId) {
        Problem problem = getById(problemId);
        
        // 检查权限：管理员或题目创建者可以删除
        if (!UserContext.isAdmin() && !UserContext.isCurrentUser(problem.getCreatorId())) {
            throw new BusinessException("无权限删除该题目");
        }
        
        removeById(problemId);
    }
}
```

### 3. 异步操作中使用

**传递用户上下文到异步线程**：
```java
@Service
public class NotificationService {
    
    @Async
    public void sendNotificationAsync(String message) {
        // 复制当前用户上下文
        UserContext.UserInfo userInfo = UserContext.copyCurrentUser();
        
        // 在新线程中设置用户上下文
        UserContext.runWithUser(userInfo, () -> {
            // 异步业务逻辑
            Long userId = UserContext.getCurrentUserId();
            // 发送通知...
        });
    }
}
```

### 4. JWT Token 使用

**生成 Token**：
```java
@Service
public class AuthService {
    
    @Autowired
    private JwtUtil jwtUtil;
    
    public LoginResponse login(LoginRequest request) {
        // 验证用户凭据
        User user = validateCredentials(request);
        
        // 生成 Token
        String token = jwtUtil.generateToken(
            user.getId(), 
            user.getUsername(), 
            UserRole.fromCode(user.getRole()),
            user.getEmail()
        );
        
        // 生成刷新 Token
        String refreshToken = jwtUtil.generateRefreshToken(
            user.getId(), 
            user.getUsername()
        );
        
        return LoginResponse.builder()
            .token(token)
            .refreshToken(refreshToken)
            .user(user)
            .build();
    }
}
```

**Token 刷新**：
```java
@RestController
@RequestMapping("/api/auth")
public class AuthController {
    
    @PostMapping("/refresh")
    public Result<TokenResponse> refreshToken(@RequestBody RefreshTokenRequest request) {
        try {
            // 验证刷新 Token
            if (!jwtUtil.validateToken(request.getRefreshToken())) {
                return Result.error("刷新Token无效");
            }
            
            // 生成新的访问 Token
            String newToken = jwtUtil.refreshToken(request.getRefreshToken());
            
            return Result.success(TokenResponse.builder()
                .token(newToken)
                .build());
        } catch (Exception e) {
            return Result.error("Token刷新失败");
        }
    }
}
```

## 🔐 权限控制

### 1. 注解权限控制

**类级别权限**：
```java
@RestController
@RequestMapping("/api/admin")
@RequireRole(UserRole.ADMIN)  // 整个控制器需要管理员权限
public class AdminController {
    // 所有方法都需要管理员权限
}
```

**方法级别权限**：
```java
@RestController
@RequestMapping("/api/user")
public class UserController {
    
    @GetMapping("/profile")
    // 无注解，只需要登录即可
    public Result<UserProfile> getProfile() { ... }
    
    @PostMapping("/admin-action")
    @RequireRole(UserRole.ADMIN)  // 特定方法需要管理员权限
    public Result<Void> adminAction() { ... }
}
```

### 2. 编程式权限检查

**在业务逻辑中检查权限**：
```java
public void updateUser(Long userId, UserUpdateRequest request) {
    // 检查是否是管理员或用户本人
    if (!UserContext.isAdmin() && !UserContext.isCurrentUser(userId)) {
        throw new BusinessException("无权限修改该用户信息");
    }
    
    // 检查特定权限
    if (!UserContext.hasPermission(UserRole.ADMIN)) {
        throw new BusinessException("需要管理员权限");
    }
    
    // 业务逻辑...
}
```

### 3. 资源访问控制

**检查资源访问权限**：
```java
public Problem getProblem(Long problemId) {
    Problem problem = getById(problemId);
    
    // 检查题目可见性
    if (problem.getStatus() != ProblemStatus.PUBLISHED) {
        // 只有管理员或创建者可以查看未发布的题目
        if (!UserContext.isAdmin() && !UserContext.isCurrentUser(problem.getCreatorId())) {
            throw new BusinessException("题目不存在或无权限访问");
        }
    }
    
    return problem;
}
```

## 🧪 测试指南

### 1. 单元测试

**模拟用户上下文**：
```java
@Test
public void testUserService() {
    // 设置测试用户上下文
    UserContext.setCurrentUser(1L, "testuser", UserRole.USER, "<EMAIL>");
    
    try {
        // 执行测试
        userService.updateProfile(request);
        
        // 验证结果
        assertEquals(1L, UserContext.getCurrentUserId());
        assertTrue(UserContext.isLoggedIn());
    } finally {
        // 清理上下文
        UserContext.clear();
    }
}
```

**测试权限检查**：
```java
@Test
public void testAdminPermission() {
    // 设置管理员用户
    UserContext.setCurrentUser(1L, "admin", UserRole.ADMIN);
    
    // 测试管理员权限
    assertTrue(UserContext.isAdmin());
    assertTrue(UserContext.hasPermission(UserRole.USER));
    
    // 设置普通用户
    UserContext.setCurrentUser(2L, "user", UserRole.USER);
    
    // 测试普通用户权限
    assertFalse(UserContext.isAdmin());
    assertFalse(UserContext.hasPermission(UserRole.ADMIN));
    
    UserContext.clear();
}
```

### 2. 集成测试

**测试 JWT 认证**：
```java
@SpringBootTest
@AutoConfigureTestDatabase
public class AuthIntegrationTest {
    
    @Autowired
    private JwtUtil jwtUtil;
    
    @Test
    public void testJwtFlow() {
        // 生成 Token
        String token = jwtUtil.generateToken(1L, "testuser", UserRole.USER, "<EMAIL>");
        
        // 验证 Token
        assertTrue(jwtUtil.validateToken(token));
        
        // 提取用户信息
        assertEquals(1L, jwtUtil.getUserIdFromToken(token));
        assertEquals("testuser", jwtUtil.getUsernameFromToken(token));
        assertEquals(UserRole.USER, jwtUtil.getUserRoleFromToken(token));
        
        // 测试 Token 刷新
        String newToken = jwtUtil.refreshToken(token);
        assertTrue(jwtUtil.validateToken(newToken));
    }
}
```

### 3. API 测试

**测试认证拦截器**：
```java
@SpringBootTest
@AutoConfigureTestDatabase
@AutoConfigureMockMvc
public class AuthInterceptorTest {
    
    @Autowired
    private MockMvc mockMvc;
    
    @Autowired
    private JwtUtil jwtUtil;
    
    @Test
    public void testAuthenticatedRequest() throws Exception {
        // 生成有效 Token
        String token = jwtUtil.generateToken(1L, "testuser", UserRole.USER);
        
        // 发送认证请求
        mockMvc.perform(get("/api/user/profile")
                .header("Authorization", "Bearer " + token))
                .andExpect(status().isOk());
    }
    
    @Test
    public void testUnauthenticatedRequest() throws Exception {
        // 发送未认证请求
        mockMvc.perform(get("/api/user/profile"))
                .andExpect(status().isUnauthorized());
    }
    
    @Test
    public void testAdminOnlyEndpoint() throws Exception {
        // 普通用户 Token
        String userToken = jwtUtil.generateToken(1L, "user", UserRole.USER);
        
        // 访问管理员接口
        mockMvc.perform(get("/api/admin/users")
                .header("Authorization", "Bearer " + userToken))
                .andExpect(status().isForbidden());
        
        // 管理员 Token
        String adminToken = jwtUtil.generateToken(2L, "admin", UserRole.ADMIN);
        
        // 访问管理员接口
        mockMvc.perform(get("/api/admin/users")
                .header("Authorization", "Bearer " + adminToken))
                .andExpected(status().isOk());
    }
}
```

## 📋 配置说明

### 1. JWT 配置

在 `application.yml` 中配置：
```yaml
jwt:
  secret: codecombined-secret-key-for-jwt-token-generation
  expiration: 86400000  # 24小时（毫秒）
  refresh: 604800000    # 7天（毫秒）
```

### 2. 拦截器配置

拦截器已在 `WebConfig` 中自动配置：
- **AuthInterceptor**：处理所有 `/api/**` 路径，排除公开接口
- **RoleInterceptor**：处理需要权限检查的路径
- **执行顺序**：AuthInterceptor → RoleInterceptor

### 3. CORS 配置

已配置跨域支持：
- 允许所有来源
- 支持所有 HTTP 方法
- 暴露 `New-Token` 头用于 Token 刷新

## 🔄 最佳实践

### 1. 用户上下文使用
- ✅ 在业务逻辑中使用 `UserContext` 获取当前用户
- ✅ 使用权限检查方法而不是直接比较角色
- ✅ 在异步操作中正确传递用户上下文
- ❌ 不要在静态方法中依赖 `UserContext`

### 2. 权限控制
- ✅ 优先使用注解进行权限控制
- ✅ 在业务逻辑中进行细粒度权限检查
- ✅ 使用资源级权限控制
- ❌ 不要在前端依赖权限控制

### 3. Token 管理
- ✅ 使用合适的过期时间
- ✅ 实现 Token 刷新机制
- ✅ 在客户端处理 Token 过期
- ❌ 不要在 URL 中传递 Token

### 4. 错误处理
- ✅ 提供清晰的错误信息
- ✅ 记录安全相关的日志
- ✅ 统一错误响应格式
- ❌ 不要暴露敏感信息

---

**总结**：UserContext 用户上下文系统已完全实现，提供了完整的用户认证、权限控制和上下文管理功能。系统支持 JWT Token 认证、角色权限检查、异步操作和完整的测试支持。🎉
