#!/bin/bash

# Spring Security 认证测试脚本
# 用于验证安全认证修复是否成功

echo "🔒 开始测试 Spring Security 认证功能..."

# 配置
BASE_URL="http://localhost:8080"
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="admin123"
USER_EMAIL="<EMAIL>"
USER_PASSWORD="admin123"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试结果
TESTS_PASSED=0
TESTS_FAILED=0

# 测试函数
test_api() {
    local test_name="$1"
    local method="$2"
    local url="$3"
    local headers="$4"
    local data="$5"
    local expected_code="$6"
    
    echo -e "\n📋 测试: ${BLUE}$test_name${NC}"
    
    if [ "$method" = "POST" ]; then
        response=$(curl -s -w "\n%{http_code}" -X POST "$url" \
            -H "Content-Type: application/json" \
            $headers \
            -d "$data")
    else
        response=$(curl -s -w "\n%{http_code}" -X GET "$url" $headers)
    fi
    
    # 分离响应体和状态码
    body=$(echo "$response" | head -n -1)
    status_code=$(echo "$response" | tail -n 1)
    
    echo "📤 请求: $method $url"
    if [ -n "$data" ]; then
        echo "📦 数据: $data"
    fi
    echo "📥 状态码: $status_code"
    echo "📄 响应: $(echo "$body" | head -c 200)..."
    
    if [ "$status_code" = "$expected_code" ]; then
        echo -e "✅ ${GREEN}测试通过${NC}"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        return 0
    else
        echo -e "❌ ${RED}测试失败${NC} (期望: $expected_code, 实际: $status_code)"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        return 1
    fi
}

# 提取 Token
extract_token() {
    local response="$1"
    echo "$response" | grep -o '"token":"[^"]*"' | cut -d'"' -f4
}

echo -e "\n🚀 开始认证测试..."

# 1. 测试管理员登录
echo -e "\n${YELLOW}=== 1. 管理员登录测试 ===${NC}"
admin_login_response=$(curl -s -X POST "$BASE_URL/api/auth/login" \
    -H "Content-Type: application/json" \
    -d "{\"email\":\"$ADMIN_EMAIL\",\"password\":\"$ADMIN_PASSWORD\"}")

echo "📋 管理员登录响应: $admin_login_response"

if echo "$admin_login_response" | grep -q '"code":200'; then
    echo -e "✅ ${GREEN}管理员登录成功${NC}"
    ADMIN_TOKEN=$(extract_token "$admin_login_response")
    echo "🔑 管理员Token: ${ADMIN_TOKEN:0:50}..."
    TESTS_PASSED=$((TESTS_PASSED + 1))
else
    echo -e "❌ ${RED}管理员登录失败${NC}"
    TESTS_FAILED=$((TESTS_FAILED + 1))
fi

# 2. 测试普通用户登录
echo -e "\n${YELLOW}=== 2. 普通用户登录测试 ===${NC}"
user_login_response=$(curl -s -X POST "$BASE_URL/api/auth/login" \
    -H "Content-Type: application/json" \
    -d "{\"email\":\"$USER_EMAIL\",\"password\":\"$USER_PASSWORD\"}")

echo "📋 普通用户登录响应: $user_login_response"

if echo "$user_login_response" | grep -q '"code":200'; then
    echo -e "✅ ${GREEN}普通用户登录成功${NC}"
    USER_TOKEN=$(extract_token "$user_login_response")
    echo "🔑 用户Token: ${USER_TOKEN:0:50}..."
    TESTS_PASSED=$((TESTS_PASSED + 1))
else
    echo -e "❌ ${RED}普通用户登录失败${NC}"
    TESTS_FAILED=$((TESTS_FAILED + 1))
fi

# 3. 测试无认证访问受保护接口
echo -e "\n${YELLOW}=== 3. 无认证访问测试 ===${NC}"
test_api "无认证访问管理接口" "GET" "$BASE_URL/api/admin/users" "" "" "401"

# 4. 测试管理员访问管理接口
if [ -n "$ADMIN_TOKEN" ]; then
    echo -e "\n${YELLOW}=== 4. 管理员权限测试 ===${NC}"
    test_api "管理员访问用户列表" "GET" "$BASE_URL/api/admin/users" "-H \"Authorization: Bearer $ADMIN_TOKEN\"" "" "200"
    test_api "管理员访问统计信息" "GET" "$BASE_URL/api/admin/users/statistics" "-H \"Authorization: Bearer $ADMIN_TOKEN\"" "" "200"
fi

# 5. 测试普通用户访问管理接口
if [ -n "$USER_TOKEN" ]; then
    echo -e "\n${YELLOW}=== 5. 普通用户权限测试 ===${NC}"
    test_api "普通用户访问管理接口" "GET" "$BASE_URL/api/admin/users" "-H \"Authorization: Bearer $USER_TOKEN\"" "" "403"
fi

# 6. 测试获取当前用户信息
if [ -n "$ADMIN_TOKEN" ]; then
    echo -e "\n${YELLOW}=== 6. 获取用户信息测试 ===${NC}"
    test_api "管理员获取用户信息" "GET" "$BASE_URL/api/auth/me" "-H \"Authorization: Bearer $ADMIN_TOKEN\"" "" "200"
fi

if [ -n "$USER_TOKEN" ]; then
    test_api "普通用户获取用户信息" "GET" "$BASE_URL/api/auth/me" "-H \"Authorization: Bearer $USER_TOKEN\"" "" "200"
fi

# 7. 测试无效Token
echo -e "\n${YELLOW}=== 7. 无效Token测试 ===${NC}"
test_api "无效Token访问" "GET" "$BASE_URL/api/auth/me" "-H \"Authorization: Bearer invalid_token\"" "" "401"

# 8. 测试公共接口
echo -e "\n${YELLOW}=== 8. 公共接口测试 ===${NC}"
test_api "检查邮箱可用性" "GET" "$BASE_URL/api/auth/check-email?email=<EMAIL>" "" "" "200"

# 9. 测试Token解析
if [ -n "$ADMIN_TOKEN" ]; then
    echo -e "\n${YELLOW}=== 9. Token解析测试 ===${NC}"
    echo "🔍 分析管理员Token结构..."
    
    # 解析JWT Token (需要安装jq)
    if command -v jq &> /dev/null; then
        # 提取payload部分
        payload=$(echo "$ADMIN_TOKEN" | cut -d'.' -f2)
        # 添加padding
        padding=$((4 - ${#payload} % 4))
        if [ $padding -ne 4 ]; then
            payload="${payload}$(printf '%*s' $padding | tr ' ' '=')"
        fi
        # Base64解码
        decoded=$(echo "$payload" | base64 -d 2>/dev/null)
        if [ $? -eq 0 ]; then
            echo "📋 Token内容: $decoded"
            if echo "$decoded" | jq -e '.role' &>/dev/null; then
                role=$(echo "$decoded" | jq -r '.role')
                echo -e "✅ ${GREEN}Token包含角色信息: $role${NC}"
                TESTS_PASSED=$((TESTS_PASSED + 1))
            else
                echo -e "❌ ${RED}Token缺少角色信息${NC}"
                TESTS_FAILED=$((TESTS_FAILED + 1))
            fi
        else
            echo -e "⚠️ ${YELLOW}无法解析Token内容${NC}"
        fi
    else
        echo -e "⚠️ ${YELLOW}需要安装jq来解析Token${NC}"
    fi
fi

# 10. 测试CORS
echo -e "\n${YELLOW}=== 10. CORS测试 ===${NC}"
cors_response=$(curl -s -w "\n%{http_code}" -X OPTIONS "$BASE_URL/api/auth/login" \
    -H "Origin: http://localhost:3000" \
    -H "Access-Control-Request-Method: POST" \
    -H "Access-Control-Request-Headers: Content-Type")

cors_status=$(echo "$cors_response" | tail -n 1)
if [ "$cors_status" = "200" ]; then
    echo -e "✅ ${GREEN}CORS配置正常${NC}"
    TESTS_PASSED=$((TESTS_PASSED + 1))
else
    echo -e "❌ ${RED}CORS配置异常${NC}"
    TESTS_FAILED=$((TESTS_FAILED + 1))
fi

# 测试结果汇总
echo -e "\n${BLUE}===========================================${NC}"
echo -e "${BLUE}           测试结果汇总${NC}"
echo -e "${BLUE}===========================================${NC}"
echo -e "✅ ${GREEN}通过测试: $TESTS_PASSED${NC}"
echo -e "❌ ${RED}失败测试: $TESTS_FAILED${NC}"
echo -e "📊 ${BLUE}总计测试: $((TESTS_PASSED + TESTS_FAILED))${NC}"

if [ $TESTS_FAILED -eq 0 ]; then
    echo -e "\n🎉 ${GREEN}所有测试通过！Spring Security 认证功能正常！${NC}"
    exit 0
else
    echo -e "\n⚠️ ${YELLOW}部分测试失败，请检查以下问题：${NC}"
    echo "1. 后端服务是否正常启动"
    echo "2. 数据库连接是否正常"
    echo "3. 测试用户是否存在"
    echo "4. JWT配置是否正确"
    echo "5. Security配置是否正确"
    exit 1
fi
