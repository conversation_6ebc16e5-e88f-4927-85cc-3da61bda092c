-- 题目管理系统数据库设计
-- 使用 MySQL 8.0+

-- 创建数据库
CREATE DATABASE IF NOT EXISTS code_combined DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE code_combined;

-- 1. 用户表
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    email VARCHAR(100) NOT NULL UNIQUE COMMENT '邮箱',
    password VARCHAR(255) NOT NULL COMMENT '密码哈希',
    nickname VARCHAR(100) COMMENT '昵称',
    avatar VARCHAR(500) COMMENT '头像URL',
    bio TEXT COMMENT '个人简介',
    location VARCHAR(100) COMMENT '所在地',
    website VARCHAR(200) COMMENT '个人网站',
    github VARCHAR(200) COMMENT 'GitHub链接',
    company VARCHAR(100) COMMENT '公司',
    position VARCHAR(100) COMMENT '职位',
    phone VARCHAR(20) COMMENT '手机号',
    birthday DATE COMMENT '生日',
    gender ENUM('MALE', 'FEMALE', 'OTHER') COMMENT '性别',
    role ENUM('USER', 'ADMIN') DEFAULT 'USER' COMMENT '角色',
    status ENUM('ACTIVE', 'INACTIVE', 'BANNED') DEFAULT 'ACTIVE' COMMENT '状态',
    points INT DEFAULT 0 COMMENT '积分',
    login_count INT DEFAULT 0 COMMENT '登录次数',
    last_login_time DATETIME COMMENT '最后登录时间',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_status (status)
) COMMENT '用户表';

-- 2. 题目表
CREATE TABLE problems (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL COMMENT '题目标题',
    description TEXT NOT NULL COMMENT '题目描述',
    difficulty ENUM('EASY', 'MEDIUM', 'HARD') NOT NULL COMMENT '难度',
    tags VARCHAR(500) COMMENT '标签，逗号分隔',
    input_format TEXT COMMENT '输入格式说明',
    output_format TEXT COMMENT '输出格式说明',
    sample_input TEXT COMMENT '示例输入',
    sample_output TEXT COMMENT '示例输出',
    constraints TEXT COMMENT '约束条件',
    time_limit INT DEFAULT 1000 COMMENT '时间限制(ms)',
    memory_limit INT DEFAULT 256 COMMENT '内存限制(MB)',
    source VARCHAR(100) COMMENT '题目来源',
    leetcode_id VARCHAR(20) COMMENT 'LeetCode题号',
    creator_id BIGINT NOT NULL COMMENT '创建者ID',
    status ENUM('DRAFT', 'PUBLISHED', 'ARCHIVED') DEFAULT 'DRAFT' COMMENT '状态',
    view_count INT DEFAULT 0 COMMENT '浏览次数',
    like_count INT DEFAULT 0 COMMENT '点赞次数',
    submission_count INT DEFAULT 0 COMMENT '提交次数',
    accepted_count INT DEFAULT 0 COMMENT '通过次数',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (creator_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_title (title),
    INDEX idx_difficulty (difficulty),
    INDEX idx_creator (creator_id),
    INDEX idx_status (status),
    INDEX idx_tags (tags),
    FULLTEXT idx_search (title, description, tags)
) COMMENT '题目表';

-- 3. 题集表
CREATE TABLE problem_sets (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(200) NOT NULL COMMENT '题集名称',
    description TEXT COMMENT '题集描述',
    cover_image VARCHAR(500) COMMENT '封面图片URL',
    creator_id BIGINT NOT NULL COMMENT '创建者ID',
    is_public BOOLEAN DEFAULT TRUE COMMENT '是否公开',
    status ENUM('DRAFT', 'PUBLISHED', 'ARCHIVED') DEFAULT 'DRAFT' COMMENT '状态',
    problem_count INT DEFAULT 0 COMMENT '题目数量',
    view_count INT DEFAULT 0 COMMENT '浏览次数',
    like_count INT DEFAULT 0 COMMENT '点赞次数',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (creator_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_name (name),
    INDEX idx_creator (creator_id),
    INDEX idx_public (is_public),
    INDEX idx_status (status),
    FULLTEXT idx_search (name, description)
) COMMENT '题集表';

-- 4. 题集题目关联表
CREATE TABLE problem_set_problems (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    problem_set_id BIGINT NOT NULL COMMENT '题集ID',
    problem_id BIGINT NOT NULL COMMENT '题目ID',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    added_by BIGINT NOT NULL COMMENT '添加者ID',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
    FOREIGN KEY (problem_set_id) REFERENCES problem_sets(id) ON DELETE CASCADE,
    FOREIGN KEY (problem_id) REFERENCES problems(id) ON DELETE CASCADE,
    FOREIGN KEY (added_by) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY uk_set_problem (problem_set_id, problem_id),
    INDEX idx_problem_set (problem_set_id),
    INDEX idx_problem (problem_id),
    INDEX idx_sort_order (sort_order)
) COMMENT '题集题目关联表';

-- 5. 用户题目提交记录表
CREATE TABLE submissions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    problem_id BIGINT NOT NULL COMMENT '题目ID',
    language VARCHAR(50) NOT NULL COMMENT '编程语言',
    code TEXT NOT NULL COMMENT '提交代码',
    status ENUM('PENDING', 'ACCEPTED', 'WRONG_ANSWER', 'TIME_LIMIT_EXCEEDED', 'MEMORY_LIMIT_EXCEEDED', 'RUNTIME_ERROR', 'COMPILE_ERROR') NOT NULL COMMENT '提交状态',
    execution_time INT COMMENT '执行时间(ms)',
    memory_usage INT COMMENT '内存使用(KB)',
    score INT DEFAULT 0 COMMENT '得分',
    error_message TEXT COMMENT '错误信息',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '提交时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (problem_id) REFERENCES problems(id) ON DELETE CASCADE,
    INDEX idx_user (user_id),
    INDEX idx_problem (problem_id),
    INDEX idx_status (status),
    INDEX idx_created_time (created_time)
) COMMENT '提交记录表';

-- 6. 用户收藏表
CREATE TABLE user_favorites (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    target_type ENUM('PROBLEM', 'PROBLEM_SET') NOT NULL COMMENT '收藏类型',
    target_id BIGINT NOT NULL COMMENT '目标ID',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '收藏时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_target (user_id, target_type, target_id),
    INDEX idx_user (user_id),
    INDEX idx_target (target_type, target_id)
) COMMENT '用户收藏表';

-- 7. 用户点赞表
CREATE TABLE user_likes (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    target_type ENUM('PROBLEM', 'PROBLEM_SET') NOT NULL COMMENT '点赞类型',
    target_id BIGINT NOT NULL COMMENT '目标ID',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '点赞时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_target (user_id, target_type, target_id),
    INDEX idx_user (user_id),
    INDEX idx_target (target_type, target_id)
) COMMENT '用户点赞表';

-- 8. 用户活动记录表
CREATE TABLE user_activities (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    activity_type ENUM('SOLVE_PROBLEM', 'CREATE_PROBLEM', 'CREATE_PROBLEM_SET', 'LOGIN', 'UPDATE_PROFILE') NOT NULL COMMENT '活动类型',
    description VARCHAR(500) NOT NULL COMMENT '活动描述',
    detail VARCHAR(1000) COMMENT '活动详情',
    points INT DEFAULT 0 COMMENT '获得积分',
    target_type VARCHAR(50) COMMENT '目标类型',
    target_id BIGINT COMMENT '目标ID',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '活动时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user (user_id),
    INDEX idx_type (activity_type),
    INDEX idx_created_time (created_time)
) COMMENT '用户活动记录表';

-- 9. 用户偏好设置表
CREATE TABLE user_preferences (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL UNIQUE COMMENT '用户ID',
    theme ENUM('LIGHT', 'DARK', 'AUTO') DEFAULT 'LIGHT' COMMENT '主题模式',
    language VARCHAR(10) DEFAULT 'zh-CN' COMMENT '界面语言',
    email_notifications BOOLEAN DEFAULT TRUE COMMENT '邮件通知',
    browser_notifications BOOLEAN DEFAULT FALSE COMMENT '浏览器通知',
    weekly_report BOOLEAN DEFAULT TRUE COMMENT '周报推送',
    public_profile BOOLEAN DEFAULT TRUE COMMENT '公开资料',
    show_email BOOLEAN DEFAULT FALSE COMMENT '显示邮箱',
    show_stats BOOLEAN DEFAULT TRUE COMMENT '显示统计',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) COMMENT '用户偏好设置表';

-- 10. 社交链接表
CREATE TABLE user_social_links (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL UNIQUE COMMENT '用户ID',
    github VARCHAR(200) COMMENT 'GitHub链接',
    leetcode VARCHAR(200) COMMENT 'LeetCode链接',
    codeforces VARCHAR(200) COMMENT 'Codeforces链接',
    atcoder VARCHAR(200) COMMENT 'AtCoder链接',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) COMMENT '用户社交链接表';

-- 创建触发器：更新题集题目数量
DELIMITER $$
CREATE TRIGGER update_problem_set_count_after_insert
AFTER INSERT ON problem_set_problems
FOR EACH ROW
BEGIN
    UPDATE problem_sets 
    SET problem_count = (
        SELECT COUNT(*) 
        FROM problem_set_problems 
        WHERE problem_set_id = NEW.problem_set_id
    )
    WHERE id = NEW.problem_set_id;
END$$

CREATE TRIGGER update_problem_set_count_after_delete
AFTER DELETE ON problem_set_problems
FOR EACH ROW
BEGIN
    UPDATE problem_sets 
    SET problem_count = (
        SELECT COUNT(*) 
        FROM problem_set_problems 
        WHERE problem_set_id = OLD.problem_set_id
    )
    WHERE id = OLD.problem_set_id;
END$$
DELIMITER ;

-- 插入初始数据
INSERT INTO users (username, email, password, nickname, role, points) VALUES
('admin', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lbdxIh0OOKmMa4Ema', '系统管理员', 'ADMIN', 2580),
('testuser', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lbdxIh0OOKmMa4Ema', '测试用户', 'USER', 450);
