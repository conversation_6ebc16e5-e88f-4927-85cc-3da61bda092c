import Mock from 'mockjs'

// 模拟题集数据
const problemSets = Mock.mock({
  'list|20': [{
    'id|+1': 1,
    'name': '@ctitle(5, 15)',
    'description': '@cparagraph(2, 4)',
    'coverImage': '@image("300x200", "@color", "@color", "题集")',
    'creatorId|1-10': 1,
    'creatorName': '@cname',
    'isPublic|0-1': 1,
    'problemCount|5-50': 1,
    'viewCount|100-1000': 1,
    'likeCount|10-100': 1,
    'status': 1,
    'createdTime': '@datetime("yyyy-MM-dd HH:mm:ss")',
    'updatedTime': '@datetime("yyyy-MM-dd HH:mm:ss")'
  }]
}).list

export default [
  // 获取题集列表
  {
    url: '/api/problemsets',
    method: 'get',
    response: ({ query }) => {
      const { current = 1, size = 10, keyword = '', isPublic } = query
      
      let filteredList = problemSets
      
      // 关键词搜索
      if (keyword) {
        filteredList = filteredList.filter(item => 
          item.name.includes(keyword) || item.description.includes(keyword)
        )
      }
      
      // 公开性筛选
      if (isPublic !== undefined) {
        filteredList = filteredList.filter(item => item.isPublic == isPublic)
      }
      
      const start = (current - 1) * size
      const end = start + parseInt(size)
      const records = filteredList.slice(start, end)
      
      return {
        code: 200,
        message: '获取成功',
        data: {
          records,
          current: parseInt(current),
          size: parseInt(size),
          total: filteredList.length,
          pages: Math.ceil(filteredList.length / size)
        },
        timestamp: Date.now()
      }
    }
  },

  // 获取热门题集
  {
    url: '/api/public/problemsets/recent',
    method: 'get',
    response: () => {
      // 返回浏览量最高的6个公开题集
      const publicSets = problemSets
        .filter(item => item.isPublic === 1)
        .sort((a, b) => b.viewCount - a.viewCount)
        .slice(0, 6)
      
      return {
        code: 200,
        message: '获取成功',
        data: publicSets,
        timestamp: Date.now()
      }
    }
  },

  // 获取题集详情
  {
    url: '/api/problemsets/:id',
    method: 'get',
    response: ({ query }) => {
      const { id } = query
      const problemSet = problemSets.find(item => item.id == id)
      
      if (!problemSet) {
        return {
          code: 404,
          message: '题集不存在',
          data: null,
          timestamp: Date.now()
        }
      }
      
      // 增加浏览量
      problemSet.viewCount += 1
      
      return {
        code: 200,
        message: '获取成功',
        data: problemSet,
        timestamp: Date.now()
      }
    }
  },

  // 创建题集
  {
    url: '/api/problemsets',
    method: 'post',
    response: ({ body, headers }) => {
      const authorization = headers.authorization
      
      if (!authorization || !authorization.startsWith('Bearer ')) {
        return {
          code: 401,
          message: '未认证，请先登录',
          data: null,
          timestamp: Date.now()
        }
      }
      
      const { name, description, isPublic = 0 } = body
      
      if (!name) {
        return {
          code: 400,
          message: '题集名称不能为空',
          data: null,
          timestamp: Date.now()
        }
      }
      
      const newProblemSet = {
        id: problemSets.length + 1,
        name,
        description: description || '',
        coverImage: Mock.Random.image("300x200", Mock.Random.color(), Mock.Random.color(), "题集"),
        creatorId: 1, // 模拟当前用户ID
        creatorName: '当前用户',
        isPublic: parseInt(isPublic),
        problemCount: 0,
        viewCount: 0,
        likeCount: 0,
        status: 1,
        createdTime: new Date().toLocaleString(),
        updatedTime: new Date().toLocaleString()
      }
      
      problemSets.push(newProblemSet)
      
      return {
        code: 200,
        message: '创建成功',
        data: newProblemSet,
        timestamp: Date.now()
      }
    }
  },

  // 更新题集
  {
    url: '/api/problemsets/:id',
    method: 'put',
    response: ({ query, body, headers }) => {
      const authorization = headers.authorization
      
      if (!authorization || !authorization.startsWith('Bearer ')) {
        return {
          code: 401,
          message: '未认证，请先登录',
          data: null,
          timestamp: Date.now()
        }
      }
      
      const { id } = query
      const problemSet = problemSets.find(item => item.id == id)
      
      if (!problemSet) {
        return {
          code: 404,
          message: '题集不存在',
          data: null,
          timestamp: Date.now()
        }
      }
      
      // 检查权限（简化版，实际应该检查是否为创建者或管理员）
      if (problemSet.creatorId !== 1) {
        return {
          code: 403,
          message: '无权限修改此题集',
          data: null,
          timestamp: Date.now()
        }
      }
      
      const { name, description, isPublic } = body
      
      if (name) problemSet.name = name
      if (description !== undefined) problemSet.description = description
      if (isPublic !== undefined) problemSet.isPublic = parseInt(isPublic)
      problemSet.updatedTime = new Date().toLocaleString()
      
      return {
        code: 200,
        message: '更新成功',
        data: problemSet,
        timestamp: Date.now()
      }
    }
  },

  // 删除题集
  {
    url: '/api/problemsets/:id',
    method: 'delete',
    response: ({ query, headers }) => {
      const authorization = headers.authorization
      
      if (!authorization || !authorization.startsWith('Bearer ')) {
        return {
          code: 401,
          message: '未认证，请先登录',
          data: null,
          timestamp: Date.now()
        }
      }
      
      const { id } = query
      const index = problemSets.findIndex(item => item.id == id)
      
      if (index === -1) {
        return {
          code: 404,
          message: '题集不存在',
          data: null,
          timestamp: Date.now()
        }
      }
      
      const problemSet = problemSets[index]
      
      // 检查权限
      if (problemSet.creatorId !== 1) {
        return {
          code: 403,
          message: '无权限删除此题集',
          data: null,
          timestamp: Date.now()
        }
      }
      
      problemSets.splice(index, 1)
      
      return {
        code: 200,
        message: '删除成功',
        data: null,
        timestamp: Date.now()
      }
    }
  },

  // 添加题目到题集
  {
    url: '/api/problemsets/:problemSetId/problems/:problemId',
    method: 'post',
    response: ({ query, headers }) => {
      const authorization = headers.authorization

      if (!authorization || !authorization.startsWith('Bearer ')) {
        return {
          code: 401,
          message: '未认证，请先登录',
          data: null,
          timestamp: Date.now()
        }
      }

      const { problemSetId, problemId } = query
      const problemSet = problemSets.find(item => item.id == problemSetId)

      if (!problemSet) {
        return {
          code: 404,
          message: '题集不存在',
          data: null,
          timestamp: Date.now()
        }
      }

      // 检查权限
      if (problemSet.creatorId !== 1) {
        return {
          code: 403,
          message: '无权限修改此题集',
          data: null,
          timestamp: Date.now()
        }
      }

      // 模拟添加成功
      problemSet.problemCount += 1
      problemSet.updatedTime = new Date().toLocaleString()

      return {
        code: 200,
        message: '题目添加成功',
        data: null,
        timestamp: Date.now()
      }
    }
  },

  // 从题集中移除题目
  {
    url: '/api/problemsets/:problemSetId/problems/:problemId',
    method: 'delete',
    response: ({ query, headers }) => {
      const authorization = headers.authorization

      if (!authorization || !authorization.startsWith('Bearer ')) {
        return {
          code: 401,
          message: '未认证，请先登录',
          data: null,
          timestamp: Date.now()
        }
      }

      const { problemSetId, problemId } = query
      const problemSet = problemSets.find(item => item.id == problemSetId)

      if (!problemSet) {
        return {
          code: 404,
          message: '题集不存在',
          data: null,
          timestamp: Date.now()
        }
      }

      // 检查权限
      if (problemSet.creatorId !== 1) {
        return {
          code: 403,
          message: '无权限修改此题集',
          data: null,
          timestamp: Date.now()
        }
      }

      // 模拟移除成功
      if (problemSet.problemCount > 0) {
        problemSet.problemCount -= 1
      }
      problemSet.updatedTime = new Date().toLocaleString()

      return {
        code: 200,
        message: '题目移除成功',
        data: null,
        timestamp: Date.now()
      }
    }
  },

  // 获取题集中的题目列表
  {
    url: '/api/problemsets/:problemSetId/problems',
    method: 'get',
    response: ({ query }) => {
      const { problemSetId } = query
      const problemSet = problemSets.find(item => item.id == problemSetId)

      if (!problemSet) {
        return {
          code: 404,
          message: '题集不存在',
          data: null,
          timestamp: Date.now()
        }
      }

      // 模拟返回题集中的题目列表
      // 这里应该从数据库中查询实际的关联关系
      // 现在用模拟数据
      const mockProblems = [
        {
          id: 1,
          title: '两数之和',
          difficulty: 'EASY',
          tags: '数组,哈希表',
          description: '给定一个整数数组 nums 和一个整数目标值 target...'
        },
        {
          id: 2,
          title: '两数相加',
          difficulty: 'MEDIUM',
          tags: '链表,数学',
          description: '给你两个非空的链表，表示两个非负的整数...'
        },
        {
          id: 3,
          title: '无重复字符的最长子串',
          difficulty: 'MEDIUM',
          tags: '哈希表,字符串,滑动窗口',
          description: '给定一个字符串 s ，请你找出其中不含有重复字符的最长子串的长度...'
        }
      ].slice(0, problemSet.problemCount || 0)

      return {
        code: 200,
        message: '获取成功',
        data: mockProblems,
        timestamp: Date.now()
      }
    }
  }
]
