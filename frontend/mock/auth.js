import Mock from 'mockjs'

// 模拟用户数据
const users = [
  {
    id: 1,
    username: 'admin',
    email: '<EMAIL>',
    password: 'admin123', // 实际项目中不会返回密码
    nickname: '管理员',
    avatar: 'https://via.placeholder.com/100x100?text=Admin',
    role: 'admin',
    status: 1,
    emailVerified: 1,
    createdTime: '2025-06-22 10:00:00'
  },
  {
    id: 2,
    username: 'testuser',
    email: '<EMAIL>',
    password: '123456',
    nickname: '测试用户',
    avatar: 'https://via.placeholder.com/100x100?text=User',
    role: 'USER',
    status: 1,
    emailVerified: 1,
    createdTime: '2025-06-22 11:00:00'
  }
]

// 生成JWT Token（模拟）
function generateToken(user) {
  const payload = {
    userId: user.id,
    username: user.username,
    role: user.role,
    exp: Math.floor(Date.now() / 1000) + 24 * 60 * 60 // 24小时后过期
  }
  // 这里只是模拟，实际JWT需要签名
  return `mock.${btoa(JSON.stringify(payload))}.signature`
}

export default [
  // 用户登录
  {
    url: '/api/auth/login',
    method: 'post',
    response: ({ body }) => {
      const { email, password } = body
      
      // 查找用户
      const user = users.find(u => u.email === email)
      
      if (!user) {
        return {
          code: 400,
          message: '邮箱或密码错误',
          data: null,
          timestamp: Date.now()
        }
      }
      
      if (user.password !== password) {
        return {
          code: 400,
          message: '邮箱或密码错误',
          data: null,
          timestamp: Date.now()
        }
      }
      
      if (user.status !== 1) {
        return {
          code: 400,
          message: '账户已被禁用',
          data: null,
          timestamp: Date.now()
        }
      }
      
      const token = generateToken(user)
      
      return {
        code: 200,
        message: '登录成功',
        data: {
          token: token,
          tokenType: 'Bearer'
        },
        timestamp: Date.now()
      }
    }
  },

  // 用户注册
  {
    url: '/api/auth/register',
    method: 'post',
    response: ({ body }) => {
      const { username, email, password, confirmPassword, nickname, emailCode } = body
      
      // 验证密码确认
      if (password !== confirmPassword) {
        return {
          code: 400,
          message: '两次输入的密码不一致',
          data: null,
          timestamp: Date.now()
        }
      }
      
      // 验证邮箱验证码（模拟）
      if (emailCode !== '123456') {
        return {
          code: 400,
          message: '邮箱验证码错误或已过期',
          data: null,
          timestamp: Date.now()
        }
      }
      
      // 检查用户名是否已存在
      if (users.find(u => u.username === username)) {
        return {
          code: 400,
          message: '用户名已存在',
          data: null,
          timestamp: Date.now()
        }
      }
      
      // 检查邮箱是否已存在
      if (users.find(u => u.email === email)) {
        return {
          code: 400,
          message: '邮箱已被注册',
          data: null,
          timestamp: Date.now()
        }
      }
      
      // 创建新用户
      const newUser = {
        id: users.length + 1,
        username,
        email,
        password,
        nickname: nickname || username,
        avatar: `https://via.placeholder.com/100x100?text=${username}`,
        role: 'USER',
        status: 1,
        emailVerified: 1,
        createdTime: new Date().toLocaleString()
      }
      
      users.push(newUser)
      
      return {
        code: 200,
        message: '注册成功',
        data: null,
        timestamp: Date.now()
      }
    }
  },

  // 发送邮箱验证码
  {
    url: '/api/auth/send-email-code',
    method: 'post',
    response: ({ query }) => {
      const { email } = query
      
      if (!email) {
        return {
          code: 400,
          message: '邮箱不能为空',
          data: null,
          timestamp: Date.now()
        }
      }
      
      // 模拟发送验证码（固定为123456）
      console.log(`模拟发送验证码到 ${email}: 123456`)
      
      return {
        code: 200,
        message: '验证码已发送，请查收邮件（模拟验证码：123456）',
        data: null,
        timestamp: Date.now()
      }
    }
  },

  // 检查用户名是否可用
  {
    url: '/api/auth/check-username',
    method: 'get',
    response: ({ query }) => {
      const { username } = query
      const available = !users.find(u => u.username === username)
      
      return {
        code: 200,
        message: '查询成功',
        data: {
          available
        },
        timestamp: Date.now()
      }
    }
  },

  // 检查邮箱是否可用
  {
    url: '/api/auth/check-email',
    method: 'get',
    response: ({ query }) => {
      const { email } = query
      const available = !users.find(u => u.email === email)
      
      return {
        code: 200,
        message: '查询成功',
        data: {
          available
        },
        timestamp: Date.now()
      }
    }
  },

  // 获取用户信息
  {
    url: '/api/user/info',
    method: 'get',
    response: ({ headers }) => {
      const authorization = headers.authorization
      
      if (!authorization || !authorization.startsWith('Bearer ')) {
        return {
          code: 401,
          message: '未认证，请先登录',
          data: null,
          timestamp: Date.now()
        }
      }
      
      try {
        const token = authorization.replace('Bearer ', '')
        const [, payloadBase64] = token.split('.')
        const payload = JSON.parse(atob(payloadBase64))
        
        const user = users.find(u => u.id === payload.userId)
        if (!user) {
          return {
            code: 401,
            message: '用户不存在',
            data: null,
            timestamp: Date.now()
          }
        }
        
        // 返回用户信息（不包含密码）
        const { password, ...userInfo } = user
        
        return {
          code: 200,
          message: '获取成功',
          data: userInfo,
          timestamp: Date.now()
        }
      } catch (error) {
        return {
          code: 401,
          message: 'Token无效',
          data: null,
          timestamp: Date.now()
        }
      }
    }
  }
]
