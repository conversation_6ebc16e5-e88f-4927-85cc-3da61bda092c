import Mock from 'mockjs'

// 模拟用户详细信息
const userProfiles = {
  1: {
    id: 1,
    username: 'admin',
    email: '<EMAIL>',
    nickname: '系统管理员',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=admin',
    bio: '热爱编程，专注于算法和数据结构的学习与分享。',
    location: '北京市',
    website: 'https://codecombined.com',
    github: 'https://github.com/admin',
    company: 'Code Combined',
    position: '技术总监',
    phone: '138****8888',
    birthday: '1990-01-01',
    gender: 'MALE',
    role: 'ADMIN',
    status: 'ACTIVE',
    joinDate: '2023-01-01',
    lastLoginTime: '2024-01-15 10:30:00',
    loginCount: 1250,
    
    // 统计信息
    stats: {
      problemsSolved: 156,
      problemsCreated: 45,
      problemSetsCreated: 12,
      totalSubmissions: 892,
      acceptedSubmissions: 678,
      acceptanceRate: 76,
      ranking: 1,
      points: 2580,
      streak: 15, // 连续刷题天数
      badges: [
        { id: 1, name: '管理员', icon: 'bi-shield-check', color: 'danger' },
        { id: 2, name: '创作者', icon: 'bi-pencil-square', color: 'primary' },
        { id: 3, name: '活跃用户', icon: 'bi-fire', color: 'warning' },
        { id: 4, name: '算法大师', icon: 'bi-trophy', color: 'success' }
      ]
    },
    
    // 偏好设置
    preferences: {
      theme: 'light', // light, dark, auto
      language: 'zh-CN',
      emailNotifications: true,
      browserNotifications: false,
      weeklyReport: true,
      publicProfile: true,
      showEmail: false,
      showStats: true
    },
    
    // 社交链接
    socialLinks: {
      github: 'https://github.com/admin',
      leetcode: 'https://leetcode.cn/u/admin',
      codeforces: 'https://codeforces.com/profile/admin',
      atcoder: 'https://atcoder.jp/users/admin'
    }
  },
  2: {
    id: 2,
    username: 'testuser',
    email: '<EMAIL>',
    nickname: '测试用户',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=testuser',
    bio: '正在学习算法和数据结构，希望能够不断进步。',
    location: '上海市',
    website: '',
    github: 'https://github.com/testuser',
    company: '',
    position: '学生',
    phone: '139****9999',
    birthday: '1995-05-15',
    gender: 'FEMALE',
    role: 'USER',
    status: 'ACTIVE',
    joinDate: '2023-06-15',
    lastLoginTime: '2024-01-14 16:45:00',
    loginCount: 89,
    
    stats: {
      problemsSolved: 23,
      problemsCreated: 2,
      problemSetsCreated: 1,
      totalSubmissions: 156,
      acceptedSubmissions: 89,
      acceptanceRate: 57,
      ranking: 156,
      points: 450,
      streak: 3,
      badges: [
        { id: 5, name: '新手', icon: 'bi-star', color: 'info' },
        { id: 6, name: '坚持者', icon: 'bi-calendar-check', color: 'success' }
      ]
    },
    
    preferences: {
      theme: 'auto',
      language: 'zh-CN',
      emailNotifications: true,
      browserNotifications: true,
      weeklyReport: false,
      publicProfile: true,
      showEmail: false,
      showStats: true
    },
    
    socialLinks: {
      github: 'https://github.com/testuser',
      leetcode: '',
      codeforces: '',
      atcoder: ''
    }
  }
}

// 模拟活动记录
const generateActivityLog = (userId) => {
  const activities = []
  const types = ['SOLVE_PROBLEM', 'CREATE_PROBLEM', 'CREATE_PROBLEMSET', 'LOGIN', 'UPDATE_PROFILE']
  const typeTexts = {
    'SOLVE_PROBLEM': '解决了题目',
    'CREATE_PROBLEM': '创建了题目',
    'CREATE_PROBLEMSET': '创建了题集',
    'LOGIN': '登录系统',
    'UPDATE_PROFILE': '更新了个人资料'
  }
  
  for (let i = 0; i < 20; i++) {
    const type = Mock.Random.pick(types)
    const date = Mock.Random.datetime('yyyy-MM-dd HH:mm:ss')
    
    activities.push({
      id: i + 1,
      type,
      description: typeTexts[type],
      detail: type === 'SOLVE_PROBLEM' ? `"${Mock.Random.pick(['两数之和', '三数之和', '最长回文子串', '二分查找'])}"` : '',
      timestamp: date,
      points: type === 'SOLVE_PROBLEM' ? Mock.Random.integer(5, 20) : 0
    })
  }
  
  return activities.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
}

export default [
  // 获取用户个人资料
  {
    url: '/api/user/profile',
    method: 'get',
    response: ({ headers }) => {
      const authorization = headers.authorization
      
      if (!authorization || !authorization.startsWith('Bearer ')) {
        return {
          code: 401,
          message: '未认证，请先登录',
          data: null,
          timestamp: Date.now()
        }
      }
      
      // 模拟从token中获取用户ID（实际应该解析JWT）
      const userId = authorization.includes('admin') ? 1 : 2
      const profile = userProfiles[userId]
      
      if (!profile) {
        return {
          code: 404,
          message: '用户不存在',
          data: null,
          timestamp: Date.now()
        }
      }
      
      return {
        code: 200,
        message: '获取成功',
        data: profile,
        timestamp: Date.now()
      }
    }
  },

  // 更新用户个人资料
  {
    url: '/api/user/profile',
    method: 'put',
    response: ({ body, headers }) => {
      const authorization = headers.authorization
      
      if (!authorization || !authorization.startsWith('Bearer ')) {
        return {
          code: 401,
          message: '未认证，请先登录',
          data: null,
          timestamp: Date.now()
        }
      }
      
      const userId = authorization.includes('admin') ? 1 : 2
      const profile = userProfiles[userId]
      
      if (!profile) {
        return {
          code: 404,
          message: '用户不存在',
          data: null,
          timestamp: Date.now()
        }
      }
      
      // 更新允许修改的字段
      const allowedFields = [
        'nickname', 'bio', 'location', 'website', 'github', 
        'company', 'position', 'phone', 'birthday', 'gender'
      ]
      
      allowedFields.forEach(field => {
        if (body[field] !== undefined) {
          profile[field] = body[field]
        }
      })
      
      return {
        code: 200,
        message: '更新成功',
        data: profile,
        timestamp: Date.now()
      }
    }
  },

  // 更新用户偏好设置
  {
    url: '/api/user/preferences',
    method: 'put',
    response: ({ body, headers }) => {
      const authorization = headers.authorization
      
      if (!authorization || !authorization.startsWith('Bearer ')) {
        return {
          code: 401,
          message: '未认证，请先登录',
          data: null,
          timestamp: Date.now()
        }
      }
      
      const userId = authorization.includes('admin') ? 1 : 2
      const profile = userProfiles[userId]
      
      if (!profile) {
        return {
          code: 404,
          message: '用户不存在',
          data: null,
          timestamp: Date.now()
        }
      }
      
      // 更新偏好设置
      Object.assign(profile.preferences, body)
      
      return {
        code: 200,
        message: '设置更新成功',
        data: profile.preferences,
        timestamp: Date.now()
      }
    }
  },

  // 更新社交链接
  {
    url: '/api/user/social-links',
    method: 'put',
    response: ({ body, headers }) => {
      const authorization = headers.authorization
      
      if (!authorization || !authorization.startsWith('Bearer ')) {
        return {
          code: 401,
          message: '未认证，请先登录',
          data: null,
          timestamp: Date.now()
        }
      }
      
      const userId = authorization.includes('admin') ? 1 : 2
      const profile = userProfiles[userId]
      
      if (!profile) {
        return {
          code: 404,
          message: '用户不存在',
          data: null,
          timestamp: Date.now()
        }
      }
      
      // 更新社交链接
      Object.assign(profile.socialLinks, body)
      
      return {
        code: 200,
        message: '社交链接更新成功',
        data: profile.socialLinks,
        timestamp: Date.now()
      }
    }
  },

  // 获取用户活动记录
  {
    url: '/api/user/activities',
    method: 'get',
    response: ({ query, headers }) => {
      const authorization = headers.authorization
      
      if (!authorization || !authorization.startsWith('Bearer ')) {
        return {
          code: 401,
          message: '未认证，请先登录',
          data: null,
          timestamp: Date.now()
        }
      }
      
      const userId = authorization.includes('admin') ? 1 : 2
      const { current = 1, size = 10 } = query
      
      const activities = generateActivityLog(userId)
      const start = (current - 1) * size
      const end = start + parseInt(size)
      const records = activities.slice(start, end)
      
      return {
        code: 200,
        message: '获取成功',
        data: {
          records,
          current: parseInt(current),
          size: parseInt(size),
          total: activities.length,
          pages: Math.ceil(activities.length / size)
        },
        timestamp: Date.now()
      }
    }
  },

  // 上传头像
  {
    url: '/api/user/avatar',
    method: 'post',
    response: ({ headers }) => {
      const authorization = headers.authorization
      
      if (!authorization || !authorization.startsWith('Bearer ')) {
        return {
          code: 401,
          message: '未认证，请先登录',
          data: null,
          timestamp: Date.now()
        }
      }
      
      // 模拟上传成功，返回新的头像URL
      const avatarUrl = `https://api.dicebear.com/7.x/avataaars/svg?seed=${Date.now()}`
      
      const userId = authorization.includes('admin') ? 1 : 2
      const profile = userProfiles[userId]
      
      if (profile) {
        profile.avatar = avatarUrl
      }
      
      return {
        code: 200,
        message: '头像上传成功',
        data: { avatarUrl },
        timestamp: Date.now()
      }
    }
  }
]
