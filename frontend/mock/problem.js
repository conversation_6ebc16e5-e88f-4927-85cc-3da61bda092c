import Mock from 'mockjs'

// 模拟题目数据
const problems = Mock.mock({
  'list|50': [{
    'id|+1': 1,
    'title': function() {
      const titles = [
        '两数之和', '两数相加', '无重复字符的最长子串', '寻找两个正序数组的中位数',
        '最长回文子串', 'Z字形变换', '整数反转', '字符串转换整数',
        '回文数', '正则表达式匹配', '盛最多水的容器', '整数转罗马数字',
        '罗马数字转整数', '最长公共前缀', '三数之和', '最接近的三数之和',
        '电话号码的字母组合', '四数之和', '删除链表的倒数第N个结点',
        '有效的括号', '合并两个有序链表', '括号生成', '合并K个升序链表',
        '两两交换链表中的节点', 'K个一组翻转链表', '删除有序数组中的重复项',
        '移除元素', '实现strStr()', '两数相除', '串联所有单词的子串',
        '下一个排列', '最长有效括号', '搜索旋转排序数组', '在排序数组中查找元素的第一个和最后一个位置',
        '搜索插入位置', '有效的数独', '解数独', '外观数列', '组合总和',
        '组合总和II', '缺失的第一个正数', '接雨水', '字符串相乘', '通配符匹配',
        '跳跃游戏II', '全排列', '全排列II', '旋转图像', '字母异位词分组'
      ]
      return Mock.Random.pick(titles)
    },
    'description': '@cparagraph(2, 4)',
    'difficulty|1': ['EASY', 'MEDIUM', 'HARD'],
    'tags': function() {
      const allTags = [
        '数组', '哈希表', '链表', '数学', '字符串', '动态规划', '二分查找',
        '分治', '回溯', '贪心', '位运算', '双指针', '排序', '栈', '队列',
        '树', '深度优先搜索', '广度优先搜索', '并查集', '图', '设计',
        '拓扑排序', '字典树', '线段树', '二叉索引树', '滑动窗口', '递归'
      ]
      const count = Mock.Random.integer(1, 4)
      const selectedTags = []
      for (let i = 0; i < count; i++) {
        const tag = Mock.Random.pick(allTags)
        if (!selectedTags.includes(tag)) {
          selectedTags.push(tag)
        }
      }
      return selectedTags.join(',')
    },
    'inputFormat': '@cparagraph(1, 2)',
    'outputFormat': '@cparagraph(1, 2)',
    'sampleInput': function() {
      return `输入：nums = [2,7,11,15], target = 9\n输入：nums = [3,2,4], target = 6`
    },
    'sampleOutput': function() {
      return `输出：[0,1]\n输出：[1,2]`
    },
    'constraints': '@cparagraph(1, 2)',
    'timeLimit|1000-5000': 1000,
    'memoryLimit|128-512': 256,
    'creatorId|1-10': 1,
    'creatorName': '@cname',
    'source': function() {
      return Mock.Random.pick(['LeetCode', '牛客网', '洛谷', 'Codeforces', '自创'])
    },
    'leetcodeId': function() {
      return Mock.Random.boolean() ? Mock.Random.integer(1, 2000).toString() : null
    },
    'status': 1,
    'createdTime': '@datetime("yyyy-MM-dd HH:mm:ss")',
    'updatedTime': '@datetime("yyyy-MM-dd HH:mm:ss")'
  }]
}).list

export default [
  // 获取题目列表
  {
    url: '/api/problems',
    method: 'get',
    response: ({ query }) => {
      const { current = 1, size = 10, keyword = '', difficulty = '', tags = '' } = query
      
      let filteredList = problems
      
      // 关键词搜索（支持标题、描述、标签）
      if (keyword) {
        const searchKeyword = keyword.toLowerCase()
        filteredList = filteredList.filter(item =>
          item.title.toLowerCase().includes(searchKeyword) ||
          item.description.toLowerCase().includes(searchKeyword) ||
          (item.tags && item.tags.toLowerCase().includes(searchKeyword))
        )
      }
      
      // 难度筛选
      if (difficulty) {
        filteredList = filteredList.filter(item => item.difficulty === difficulty)
      }
      
      // 标签筛选
      if (tags) {
        const searchTags = tags.split(',').map(tag => tag.trim())
        filteredList = filteredList.filter(item => 
          searchTags.some(tag => item.tags.includes(tag))
        )
      }
      
      const start = (current - 1) * size
      const end = start + parseInt(size)
      const records = filteredList.slice(start, end)
      
      return {
        code: 200,
        message: '获取成功',
        data: {
          records,
          current: parseInt(current),
          size: parseInt(size),
          total: filteredList.length,
          pages: Math.ceil(filteredList.length / size)
        },
        timestamp: Date.now()
      }
    }
  },

  // 获取题目详情
  {
    url: '/api/problems/:id',
    method: 'get',
    response: ({ query }) => {
      const { id } = query
      const problem = problems.find(item => item.id == id)
      
      if (!problem) {
        return {
          code: 404,
          message: '题目不存在',
          data: null,
          timestamp: Date.now()
        }
      }
      
      return {
        code: 200,
        message: '获取成功',
        data: problem,
        timestamp: Date.now()
      }
    }
  },

  // 创建题目
  {
    url: '/api/problems',
    method: 'post',
    response: ({ body, headers }) => {
      const authorization = headers.authorization
      
      if (!authorization || !authorization.startsWith('Bearer ')) {
        return {
          code: 401,
          message: '未认证，请先登录',
          data: null,
          timestamp: Date.now()
        }
      }
      
      const { title, description, difficulty, tags } = body
      
      if (!title) {
        return {
          code: 400,
          message: '题目标题不能为空',
          data: null,
          timestamp: Date.now()
        }
      }
      
      if (!description) {
        return {
          code: 400,
          message: '题目描述不能为空',
          data: null,
          timestamp: Date.now()
        }
      }
      
      if (!difficulty) {
        return {
          code: 400,
          message: '题目难度不能为空',
          data: null,
          timestamp: Date.now()
        }
      }
      
      const newProblem = {
        id: problems.length + 1,
        title,
        description,
        difficulty,
        tags: tags || '',
        inputFormat: body.inputFormat || '',
        outputFormat: body.outputFormat || '',
        sampleInput: body.sampleInput || '',
        sampleOutput: body.sampleOutput || '',
        constraints: body.constraints || '',
        timeLimit: body.timeLimit || 1000,
        memoryLimit: body.memoryLimit || 256,
        creatorId: 1, // 模拟当前用户ID
        creatorName: '当前用户',
        source: body.source || '自创',
        leetcodeId: body.leetcodeId || null,
        status: 1,
        createdTime: new Date().toLocaleString(),
        updatedTime: new Date().toLocaleString()
      }
      
      problems.push(newProblem)
      
      return {
        code: 200,
        message: '创建成功',
        data: newProblem,
        timestamp: Date.now()
      }
    }
  },

  // 更新题目
  {
    url: '/api/problems/:id',
    method: 'put',
    response: ({ query, body, headers }) => {
      const authorization = headers.authorization
      
      if (!authorization || !authorization.startsWith('Bearer ')) {
        return {
          code: 401,
          message: '未认证，请先登录',
          data: null,
          timestamp: Date.now()
        }
      }
      
      const { id } = query
      const problem = problems.find(item => item.id == id)
      
      if (!problem) {
        return {
          code: 404,
          message: '题目不存在',
          data: null,
          timestamp: Date.now()
        }
      }
      
      // 检查权限（简化版，实际应该检查是否为创建者或管理员）
      if (problem.creatorId !== 1) {
        return {
          code: 403,
          message: '无权限修改此题目',
          data: null,
          timestamp: Date.now()
        }
      }
      
      // 更新字段
      const updateFields = [
        'title', 'description', 'difficulty', 'tags', 'inputFormat',
        'outputFormat', 'sampleInput', 'sampleOutput', 'constraints',
        'timeLimit', 'memoryLimit', 'source', 'leetcodeId'
      ]
      
      updateFields.forEach(field => {
        if (body[field] !== undefined) {
          problem[field] = body[field]
        }
      })
      
      problem.updatedTime = new Date().toLocaleString()
      
      return {
        code: 200,
        message: '更新成功',
        data: problem,
        timestamp: Date.now()
      }
    }
  },

  // 删除题目
  {
    url: '/api/problems/:id',
    method: 'delete',
    response: ({ query, headers }) => {
      const authorization = headers.authorization
      
      if (!authorization || !authorization.startsWith('Bearer ')) {
        return {
          code: 401,
          message: '未认证，请先登录',
          data: null,
          timestamp: Date.now()
        }
      }
      
      const { id } = query
      const index = problems.findIndex(item => item.id == id)
      
      if (index === -1) {
        return {
          code: 404,
          message: '题目不存在',
          data: null,
          timestamp: Date.now()
        }
      }
      
      const problem = problems[index]
      
      // 检查权限
      if (problem.creatorId !== 1) {
        return {
          code: 403,
          message: '无权限删除此题目',
          data: null,
          timestamp: Date.now()
        }
      }
      
      problems.splice(index, 1)
      
      return {
        code: 200,
        message: '删除成功',
        data: null,
        timestamp: Date.now()
      }
    }
  }
]
