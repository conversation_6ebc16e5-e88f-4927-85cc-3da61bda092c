import Mock from 'mockjs'

export default [
  // 获取用户统计信息（公开接口）
  {
    url: '/api/stats/public/users',
    method: 'get',
    response: () => {
      return {
        code: 200,
        message: '获取成功',
        data: {
          onlineUsers: Mock.Random.integer(10, 50),
          totalUsers: Mock.Random.integer(1000, 5000)
        },
        timestamp: Date.now()
      }
    }
  },

  // 获取平台统计信息（公开接口）
  {
    url: '/api/stats/public/platform',
    method: 'get',
    response: () => {
      return {
        code: 200,
        message: '获取成功',
        data: {
          totalProblemSets: Mock.Random.integer(100, 500),
          totalProblems: Mock.Random.integer(500, 2000)
        },
        timestamp: Date.now()
      }
    }
  },

  // 获取控制台统计信息
  {
    url: '/api/stats/dashboard',
    method: 'get',
    response: () => {
      return {
        code: 200,
        message: '获取成功',
        data: {
          onlineUsers: Mock.Random.integer(10, 50),
          totalUsers: Mock.Random.integer(1000, 5000),
          totalProblemSets: Mock.Random.integer(100, 500),
          totalProblems: Mock.Random.integer(500, 2000),
          publicProblemSets: Mock.Random.integer(50, 300),
          todayRegistrations: Mock.Random.integer(5, 20)
        },
        timestamp: Date.now()
      }
    }
  }
]
