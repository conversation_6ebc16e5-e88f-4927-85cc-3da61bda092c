{"name": "code-combined-frontend", "version": "1.0.0", "description": "Code-Combined算法题集网站前端", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"vue": "^3.3.4", "vue-router": "^4.2.4", "vuex": "^4.1.0", "axios": "^1.4.0", "bootstrap": "^5.3.0", "@popperjs/core": "^2.11.8", "bootstrap-icons": "^1.10.5", "marked": "^5.1.1", "highlight.js": "^11.8.0", "socket.io-client": "^4.7.2", "sweetalert2": "^11.7.20", "nprogress": "^0.2.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.2.3", "vite": "^4.4.5", "eslint": "^8.45.0", "eslint-plugin-vue": "^9.15.1", "@vue/eslint-config-prettier": "^8.0.0", "prettier": "^3.0.0", "mockjs": "^1.1.0", "vite-plugin-mock": "^3.0.0"}, "keywords": ["vue3", "bootstrap", "algorithm", "leetcode", "problem-set"], "author": "Code-Combined Team", "license": "MIT"}