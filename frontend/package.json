{"name": "code-combined-frontend", "version": "1.0.0", "description": "Code-Combined算法题集网站前端", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx --fix --ignore-path .gitignore", "type-check": "vue-tsc --noEmit"}, "dependencies": {"vue": "^3.3.4", "vue-router": "^4.2.4", "vuex": "^4.1.0", "axios": "^1.4.0", "bootstrap": "^5.3.0", "@popperjs/core": "^2.11.8", "bootstrap-icons": "^1.10.5", "marked": "^5.1.1", "highlight.js": "^11.8.0", "socket.io-client": "^4.7.2", "sweetalert2": "^11.7.20", "nprogress": "^0.2.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.2.3", "vite": "^4.4.5", "typescript": "^5.0.2", "vue-tsc": "^1.4.2", "@types/node": "^20.3.1", "@types/nprogress": "^0.2.0", "eslint": "^8.45.0", "eslint-plugin-vue": "^9.15.1", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^11.0.3", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "prettier": "^3.0.0"}, "keywords": ["vue3", "bootstrap", "algorithm", "leetcode", "problem-set"], "author": "Code-Combined Team", "license": "MIT"}