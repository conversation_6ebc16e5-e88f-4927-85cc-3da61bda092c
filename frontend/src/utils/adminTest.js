// 管理员功能测试工具
export class AdminTestHelper {
  constructor(store, router) {
    this.store = store
    this.router = router
  }

  // 快速登录为管理员
  async quickLoginAsAdmin() {
    try {
      const adminUser = {
        id: 1,
        username: 'admin',
        email: '<EMAIL>',
        nickname: '系统管理员',
        role: 'admin',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=admin',
        points: 9999,
        status: 'active'
      }

      const token = 'mock-admin-token-' + Date.now()
      
      // 设置到 store
      this.store.commit('auth/SET_USER', adminUser)
      this.store.commit('auth/SET_TOKEN', token)
      
      // 设置到 localStorage
      localStorage.setItem('token', token)
      
      console.log('✅ 管理员登录成功:', adminUser)
      return { success: true, user: adminUser }
    } catch (error) {
      console.error('❌ 管理员登录失败:', error)
      return { success: false, error }
    }
  }

  // 验证管理员权限
  checkAdminPermissions() {
    const results = {
      isAuthenticated: false,
      hasUser: false,
      isAdmin: false,
      hasAdminRole: false,
      canAccessAdmin: false
    }

    try {
      // 检查认证状态
      results.isAuthenticated = this.store.getters['auth/isAuthenticated']
      
      // 检查用户信息
      const user = this.store.getters['auth/currentUser']
      results.hasUser = !!user
      
      if (user) {
        // 检查角色
        results.hasAdminRole = user.role === 'admin'
        results.isAdmin = this.store.getters['auth/isAdmin']
        
        // 检查是否能访问管理页面
        results.canAccessAdmin = results.isAuthenticated && results.hasAdminRole
      }

      console.log('🔍 管理员权限检查结果:', results)
      return results
    } catch (error) {
      console.error('❌ 权限检查失败:', error)
      return results
    }
  }

  // 检查管理员入口显示
  checkAdminEntries() {
    const entries = {
      topButton: false,
      permissionCard: false,
      quickAction: false,
      profileButton: false,
      navbarDropdown: false,
      total: 0
    }

    try {
      // 检查页面顶部按钮
      const topButtons = document.querySelectorAll('a[href="/admin"], button[onclick*="admin"]')
      entries.topButton = topButtons.length > 0

      // 检查权限提示卡片
      const permissionCards = document.querySelectorAll('.border-danger, .card-danger')
      entries.permissionCard = permissionCards.length > 0

      // 检查快速操作按钮
      const quickActions = document.querySelectorAll('.btn-danger')
      entries.quickAction = Array.from(quickActions).some(btn => 
        btn.textContent.includes('管理') || btn.textContent.includes('控制台')
      )

      // 检查个人信息按钮
      const profileButtons = document.querySelectorAll('.btn-sm')
      entries.profileButton = Array.from(profileButtons).some(btn => 
        btn.textContent.includes('管理控制台')
      )

      // 检查导航栏下拉菜单
      const navDropdowns = document.querySelectorAll('.dropdown-item')
      entries.navbarDropdown = Array.from(navDropdowns).some(item => 
        item.textContent.includes('管理控制台')
      )

      // 计算总数
      entries.total = Object.values(entries).filter(v => v === true).length

      console.log('🔍 管理员入口检查结果:', entries)
      return entries
    } catch (error) {
      console.error('❌ 入口检查失败:', error)
      return entries
    }
  }

  // 测试管理页面访问
  async testAdminPageAccess() {
    try {
      console.log('🧪 测试管理页面访问...')
      
      // 尝试导航到管理页面
      await this.router.push('/admin')
      
      // 等待页面加载
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 检查是否成功到达管理页面
      const currentPath = this.router.currentRoute.value.path
      const isOnAdminPage = currentPath === '/admin'
      
      if (isOnAdminPage) {
        console.log('✅ 成功访问管理页面')
        return { success: true, path: currentPath }
      } else {
        console.log('❌ 未能访问管理页面，当前路径:', currentPath)
        return { success: false, path: currentPath }
      }
    } catch (error) {
      console.error('❌ 管理页面访问测试失败:', error)
      return { success: false, error }
    }
  }

  // 测试管理功能模块
  testAdminModules() {
    const modules = {
      overview: false,
      users: false,
      problems: false,
      problemsets: false,
      settings: false
    }

    try {
      // 检查侧边栏菜单项
      const menuItems = document.querySelectorAll('.menu-item, .nav-link')
      
      menuItems.forEach(item => {
        const text = item.textContent.toLowerCase()
        if (text.includes('概览') || text.includes('仪表盘')) {
          modules.overview = true
        }
        if (text.includes('用户管理')) {
          modules.users = true
        }
        if (text.includes('题目管理')) {
          modules.problems = true
        }
        if (text.includes('题集管理')) {
          modules.problemsets = true
        }
        if (text.includes('系统设置')) {
          modules.settings = true
        }
      })

      console.log('🔍 管理模块检查结果:', modules)
      return modules
    } catch (error) {
      console.error('❌ 模块检查失败:', error)
      return modules
    }
  }

  // 完整的管理员功能测试
  async runFullTest() {
    console.log('🚀 开始完整的管理员功能测试...')
    
    const testResults = {
      login: null,
      permissions: null,
      entries: null,
      pageAccess: null,
      modules: null,
      overall: false
    }

    try {
      // 1. 测试登录
      console.log('1️⃣ 测试管理员登录...')
      testResults.login = await this.quickLoginAsAdmin()

      // 2. 检查权限
      console.log('2️⃣ 检查管理员权限...')
      testResults.permissions = this.checkAdminPermissions()

      // 3. 检查入口显示
      console.log('3️⃣ 检查管理员入口...')
      testResults.entries = this.checkAdminEntries()

      // 4. 测试页面访问
      console.log('4️⃣ 测试管理页面访问...')
      testResults.pageAccess = await this.testAdminPageAccess()

      // 5. 检查功能模块
      console.log('5️⃣ 检查管理功能模块...')
      testResults.modules = this.testAdminModules()

      // 6. 综合评估
      testResults.overall = (
        testResults.login?.success &&
        testResults.permissions?.canAccessAdmin &&
        testResults.entries?.total > 0 &&
        testResults.pageAccess?.success
      )

      // 输出测试报告
      this.printTestReport(testResults)
      
      return testResults
    } catch (error) {
      console.error('❌ 完整测试失败:', error)
      testResults.overall = false
      return testResults
    }
  }

  // 打印测试报告
  printTestReport(results) {
    console.log('\n📊 管理员功能测试报告')
    console.log('=' .repeat(50))
    
    console.log(`登录测试: ${results.login?.success ? '✅ 通过' : '❌ 失败'}`)
    console.log(`权限检查: ${results.permissions?.canAccessAdmin ? '✅ 通过' : '❌ 失败'}`)
    console.log(`入口显示: ${results.entries?.total > 0 ? '✅ 通过' : '❌ 失败'} (${results.entries?.total}/5)`)
    console.log(`页面访问: ${results.pageAccess?.success ? '✅ 通过' : '❌ 失败'}`)
    console.log(`功能模块: ${Object.values(results.modules || {}).some(v => v) ? '✅ 通过' : '❌ 失败'}`)
    
    console.log('=' .repeat(50))
    console.log(`总体结果: ${results.overall ? '✅ 全部通过' : '❌ 存在问题'}`)
    
    if (!results.overall) {
      console.log('\n🔧 建议检查:')
      if (!results.login?.success) {
        console.log('- 检查登录逻辑和用户数据')
      }
      if (!results.permissions?.canAccessAdmin) {
        console.log('- 检查权限验证逻辑')
      }
      if (results.entries?.total === 0) {
        console.log('- 检查管理员入口显示逻辑')
      }
      if (!results.pageAccess?.success) {
        console.log('- 检查路由守卫和页面访问权限')
      }
    }
  }

  // 修复常见问题
  async fixCommonIssues() {
    console.log('🔧 尝试修复常见问题...')
    
    try {
      // 1. 确保用户角色正确
      const user = this.store.getters['auth/currentUser']
      if (user && user.role !== 'admin') {
        console.log('修复用户角色...')
        user.role = 'admin'
        this.store.commit('auth/SET_USER', user)
      }

      // 2. 确保认证状态正确
      if (!this.store.getters['auth/isAuthenticated']) {
        console.log('修复认证状态...')
        const token = localStorage.getItem('token') || 'mock-admin-token'
        this.store.commit('auth/SET_TOKEN', token)
      }

      // 3. 刷新页面以应用更改
      console.log('刷新页面以应用更改...')
      window.location.reload()
      
      return true
    } catch (error) {
      console.error('❌ 修复失败:', error)
      return false
    }
  }
}

// 全局测试函数
export function createAdminTester(store, router) {
  return new AdminTestHelper(store, router)
}

// 开发环境下添加到 window 对象
if (process.env.NODE_ENV === 'development') {
  window.AdminTestHelper = AdminTestHelper
  
  // 添加快速测试命令
  window.testAdmin = function() {
    if (window.app && window.app.$store && window.app.$router) {
      const tester = new AdminTestHelper(window.app.$store, window.app.$router)
      return tester.runFullTest()
    } else {
      console.error('❌ 无法访问 Vue 应用实例')
    }
  }
  
  window.fixAdmin = function() {
    if (window.app && window.app.$store && window.app.$router) {
      const tester = new AdminTestHelper(window.app.$store, window.app.$router)
      return tester.fixCommonIssues()
    } else {
      console.error('❌ 无法访问 Vue 应用实例')
    }
  }
  
  console.log('🔧 管理员测试工具已加载:')
  console.log('- window.testAdmin() // 运行完整测试')
  console.log('- window.fixAdmin()  // 修复常见问题')
}
