// 消息提示工具类

export interface MessageOptions {
  message: string
  duration?: number
  type?: 'success' | 'error' | 'warning' | 'info'
  showClose?: boolean
}

export interface ConfirmOptions {
  title?: string
  content: string
  type?: 'warning' | 'info' | 'error'
  okText?: string
  cancelText?: string
  onOk?: () => void | Promise<void>
  onCancel?: () => void
}

// 创建消息元素
function createMessageElement(options: MessageOptions): HTMLElement {
  const messageEl = document.createElement('div')
  messageEl.className = `alert alert-${getBootstrapType(options.type || 'info')} alert-dismissible fade show message-toast`
  messageEl.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    min-width: 300px;
    max-width: 500px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-radius: 6px;
    animation: slideInRight 0.3s ease-out;
  `
  
  const icon = getIcon(options.type || 'info')
  messageEl.innerHTML = `
    <div class="d-flex align-items-center">
      <i class="${icon} me-2"></i>
      <span>${options.message}</span>
      ${options.showClose !== false ? '<button type="button" class="btn-close ms-auto" aria-label="Close"></button>' : ''}
    </div>
  `
  
  return messageEl
}

// 获取Bootstrap类型
function getBootstrapType(type: string): string {
  const typeMap: Record<string, string> = {
    success: 'success',
    error: 'danger',
    warning: 'warning',
    info: 'info'
  }
  return typeMap[type] || 'info'
}

// 获取图标
function getIcon(type: string): string {
  const iconMap: Record<string, string> = {
    success: 'bi bi-check-circle-fill',
    error: 'bi bi-x-circle-fill',
    warning: 'bi bi-exclamation-triangle-fill',
    info: 'bi bi-info-circle-fill'
  }
  return iconMap[type] || 'bi bi-info-circle-fill'
}

// 显示消息
function showMessage(options: MessageOptions): void {
  const messageEl = createMessageElement(options)
  document.body.appendChild(messageEl)
  
  // 添加关闭事件
  const closeBtn = messageEl.querySelector('.btn-close')
  if (closeBtn) {
    closeBtn.addEventListener('click', () => {
      removeMessage(messageEl)
    })
  }
  
  // 自动关闭
  const duration = options.duration !== undefined ? options.duration : 3000
  if (duration > 0) {
    setTimeout(() => {
      removeMessage(messageEl)
    }, duration)
  }
}

// 移除消息
function removeMessage(messageEl: HTMLElement): void {
  messageEl.style.animation = 'slideOutRight 0.3s ease-in'
  setTimeout(() => {
    if (messageEl.parentNode) {
      messageEl.parentNode.removeChild(messageEl)
    }
  }, 300)
}

// 创建确认对话框
function createConfirmModal(options: ConfirmOptions): HTMLElement {
  const modalId = 'confirm-modal-' + Date.now()
  const modal = document.createElement('div')
  modal.className = 'modal fade'
  modal.id = modalId
  modal.setAttribute('tabindex', '-1')
  modal.innerHTML = `
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">
            <i class="${getConfirmIcon(options.type || 'warning')} me-2"></i>
            ${options.title || '确认'}
          </h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
          ${options.content}
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
            ${options.cancelText || '取消'}
          </button>
          <button type="button" class="btn btn-${getConfirmButtonType(options.type || 'warning')}" id="confirm-ok-btn">
            ${options.okText || '确定'}
          </button>
        </div>
      </div>
    </div>
  `
  
  return modal
}

// 获取确认对话框图标
function getConfirmIcon(type: string): string {
  const iconMap: Record<string, string> = {
    warning: 'bi bi-exclamation-triangle text-warning',
    info: 'bi bi-info-circle text-info',
    error: 'bi bi-x-circle text-danger'
  }
  return iconMap[type] || 'bi bi-exclamation-triangle text-warning'
}

// 获取确认按钮类型
function getConfirmButtonType(type: string): string {
  const typeMap: Record<string, string> = {
    warning: 'warning',
    info: 'primary',
    error: 'danger'
  }
  return typeMap[type] || 'warning'
}

// 显示确认对话框
function showConfirm(options: ConfirmOptions): void {
  const modal = createConfirmModal(options)
  document.body.appendChild(modal)
  
  // 使用Bootstrap Modal
  const bsModal = new (window as any).bootstrap.Modal(modal)
  bsModal.show()
  
  // 绑定确定按钮事件
  const okBtn = modal.querySelector('#confirm-ok-btn')
  if (okBtn) {
    okBtn.addEventListener('click', async () => {
      try {
        if (options.onOk) {
          await options.onOk()
        }
        bsModal.hide()
      } catch (error) {
        console.error('确认操作失败:', error)
      }
    })
  }
  
  // 绑定取消事件
  modal.addEventListener('hidden.bs.modal', () => {
    if (options.onCancel) {
      options.onCancel()
    }
    document.body.removeChild(modal)
  })
}

// 导出的消息方法
export function showSuccess(message: string, duration?: number): void {
  showMessage({ message, type: 'success', duration })
}

export function showError(message: string, duration?: number): void {
  showMessage({ message, type: 'error', duration })
}

export function showWarning(message: string, duration?: number): void {
  showMessage({ message, type: 'warning', duration })
}

export function showInfo(message: string, duration?: number): void {
  showMessage({ message, type: 'info', duration })
}

export function confirm(options: ConfirmOptions): void {
  showConfirm(options)
}

// 添加CSS动画
const style = document.createElement('style')
style.textContent = `
  @keyframes slideInRight {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
  
  @keyframes slideOutRight {
    from {
      transform: translateX(0);
      opacity: 1;
    }
    to {
      transform: translateX(100%);
      opacity: 0;
    }
  }
  
  .message-toast {
    transition: all 0.3s ease;
  }
`
document.head.appendChild(style)
