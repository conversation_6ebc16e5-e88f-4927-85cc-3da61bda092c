import { createRouter, createWebHistory, type RouteRecordRaw } from 'vue-router'
import store from '@/store'
import NProgress from 'nprogress'
import type { RouteMetaCustom } from '@/types'

// 扩展路由元信息类型
declare module 'vue-router' {
  interface RouteMeta extends RouteMetaCustom {}
}

// 路由组件 - 使用动态导入
const Home = () => import('@/views/Home.vue')
const Login = () => import('@/views/auth/Login.vue')
const Register = () => import('@/views/auth/Register.vue')
const ProblemSets = () => import('@/views/problemset/ProblemSets.vue')
const ProblemSetDetail = () => import('@/views/problemset/ProblemSetDetail.vue')
const CreateProblemSet = () => import('@/views/problemset/CreateProblemSet.vue')
const Problems = () => import('@/views/problem/Problems.vue')
const ProblemDetail = () => import('@/views/problem/ProblemDetail.vue')
const CreateProblem = () => import('@/views/problem/CreateProblem.vue')
const Profile = () => import('@/views/user/Profile.vue')
const Dashboard = () => import('@/views/Dashboard.vue')
const AdminDashboard = () => import('@/views/admin/AdminDashboard.vue')
const NotFound = () => import('@/views/error/NotFound.vue')

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: { title: '首页' }
  },
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: { title: '登录', guest: true }
  },
  {
    path: '/register',
    name: 'Register',
    component: Register,
    meta: { title: '注册', guest: true }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: Dashboard,
    meta: { title: '控制台', requiresAuth: true }
  },
  {
    path: '/problemsets',
    name: 'ProblemSets',
    component: ProblemSets,
    meta: { title: '题集列表' }
  },
  {
    path: '/problemsets/create',
    name: 'CreateProblemSet',
    component: CreateProblemSet,
    meta: { title: '创建题集', requiresAuth: true }
  },
  {
    path: '/problemsets/:id',
    name: 'ProblemSetDetail',
    component: ProblemSetDetail,
    meta: { title: '题集详情' },
    props: true
  },
  {
    path: '/problems',
    name: 'Problems',
    component: Problems,
    meta: { title: '题目列表' }
  },
  {
    path: '/problems/create',
    name: 'CreateProblem',
    component: CreateProblem,
    meta: { title: '创建题目', requiresAuth: true }
  },
  {
    path: '/problems/:id',
    name: 'ProblemDetail',
    component: ProblemDetail,
    meta: { title: '题目详情' },
    props: true
  },
  {
    path: '/profile',
    name: 'Profile',
    component: Profile,
    meta: { title: '个人资料', requiresAuth: true }
  },
  {
    path: '/admin',
    name: 'AdminDashboard',
    component: AdminDashboard,
    meta: { title: '管理控制台', requiresAuth: true, requiresAdmin: true }
  },
  {
    path: '/404',
    name: 'NotFound',
    component: NotFound,
    meta: { title: '页面未找到' }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫 (无认证模式)
router.beforeEach(async (to, from, next) => {
  NProgress.start()
  
  // 设置页面标题
  document.title = to.meta.title ? `${to.meta.title} - Code-Combined` : 'Code-Combined'
  
  // 无认证模式：跳过所有认证检查
  console.log('无认证模式：允许访问所有路由')
  
  // 尝试获取用户信息（如果还没有的话）
  const currentUser = store.getters['auth/currentUser']
  if (!currentUser) {
    try {
      await store.dispatch('auth/getUserInfo')
    } catch (error) {
      console.warn('获取用户信息失败，但在无认证模式下继续:', (error as Error).message)
    }
  }
  
  next()
})

router.afterEach(() => {
  NProgress.done()
})

export default router
