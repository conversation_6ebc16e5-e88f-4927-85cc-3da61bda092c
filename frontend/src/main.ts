import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'

// Bootstrap CSS
import 'bootstrap/dist/css/bootstrap.min.css'
import 'bootstrap-icons/font/bootstrap-icons.css'

// Bootstrap JS
import 'bootstrap/dist/js/bootstrap.bundle.min.js'

// 自定义样式
import './assets/css/main.css'

// 进度条
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

// 类型导入
import type { App as VueApp } from 'vue'

// 配置进度条
NProgress.configure({ 
  showSpinner: false,
  minimum: 0.2,
  speed: 500
})

const app: VueApp = createApp(App)

app.use(store)
app.use(router)

// 全局属性
app.config.globalProperties.$message = {
  success: (message: string) => {
    console.log('Success:', message)
    // TODO: 实现消息提示组件
  },
  error: (message: string) => {
    console.error('Error:', message)
    // TODO: 实现消息提示组件
  },
  warning: (message: string) => {
    console.warn('Warning:', message)
    // TODO: 实现消息提示组件
  },
  info: (message: string) => {
    console.info('Info:', message)
    // TODO: 实现消息提示组件
  }
}

app.config.globalProperties.$confirm = (options: {
  title?: string
  content: string
  onOk?: () => void | Promise<void>
  onCancel?: () => void
}) => {
  // TODO: 实现确认对话框组件
  const confirmed = window.confirm(`${options.title || '确认'}\n${options.content}`)
  if (confirmed && options.onOk) {
    options.onOk()
  } else if (!confirmed && options.onCancel) {
    options.onCancel()
  }
}

app.config.globalProperties.$loading = {
  show: (message?: string) => {
    NProgress.start()
    console.log('Loading:', message || '加载中...')
  },
  hide: () => {
    NProgress.done()
  }
}

app.mount('#app')
