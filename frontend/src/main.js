import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'

// Bootstrap CSS
import 'bootstrap/dist/css/bootstrap.min.css'
import 'bootstrap-icons/font/bootstrap-icons.css'

// Bootstrap JS
import 'bootstrap/dist/js/bootstrap.bundle.min.js'

// 自定义样式
import './assets/css/main.css'

// 进度条
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

// 配置进度条
NProgress.configure({ 
  showSpinner: false,
  minimum: 0.2,
  speed: 500
})

const app = createApp(App)

app.use(store)
app.use(router)

// 开发环境下添加管理员测试工具
if (process.env.NODE_ENV === 'development') {
  import('./utils/adminTest.js').then(({ AdminTestHelper }) => {
    // 将 Vue 应用实例添加到 window 对象，供测试工具使用
    window.app = app

    // 创建全局测试实例
    window.adminTester = new AdminTestHelper(store, router)

    // 添加快速测试命令
    window.testAdmin = () => window.adminTester.runFullTest()
    window.fixAdmin = () => window.adminTester.fixCommonIssues()
    window.loginAsAdmin = () => window.adminTester.quickLoginAsAdmin()

    console.log('🔧 管理员测试工具已加载:')
    console.log('- window.testAdmin()   // 运行完整测试')
    console.log('- window.fixAdmin()    // 修复常见问题')
    console.log('- window.loginAsAdmin() // 快速登录为管理员')
  })
}

app.mount('#app')
