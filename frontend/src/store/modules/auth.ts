import api from '@/utils/api'
import { removeToken, setToken, getToken } from '@/utils/auth'
import type { Module } from 'vuex'
import type { RootState } from '@/store'
import type { User, LoginRequest, RegisterRequest, ApiResponse } from '@/types'

// 定义 auth 模块的状态类型
export interface AuthState {
  token: string | null
  user: User | null
  loading: boolean
}

const state: AuthState = {
  token: getToken(),
  user: null,
  loading: false
}

const mutations = {
  SET_TOKEN(state: AuthState, token: string | null) {
    state.token = token
  },
  SET_USER(state: AuthState, user: User | null) {
    state.user = user
  },
  SET_LOADING(state: AuthState, loading: boolean) {
    state.loading = loading
  },
  CLEAR_AUTH(state: AuthState) {
    state.token = null
    state.user = null
  }
}

const actions = {
  // 登录 (无认证模式)
  async login({ commit }: any, loginData: LoginRequest): Promise<ApiResponse> {
    commit('SET_LOADING', true)
    try {
      // 直接使用后端 API
      const response = await api.post('/auth/login', loginData)
      const { user } = response.data

      // 无认证模式：直接设置用户信息，不使用Token
      commit('SET_USER', user)
      commit('SET_TOKEN', 'no-auth-mode') // 设置一个标识

      return response
    } finally {
      commit('SET_LOADING', false)
    }
  },

  // 直接设置认证信息（用于快速登录）
  async setAuth({ commit }: any, { token, user }: { token: string; user: User }) {
    commit('SET_TOKEN', token)
    commit('SET_USER', user)
    setToken(token)
    return { token, user }
  },

  // 注册
  async register({ commit }: any, registerData: RegisterRequest): Promise<ApiResponse> {
    commit('SET_LOADING', true)
    try {
      const response = await api.post('/auth/register', registerData)
      return response
    } finally {
      commit('SET_LOADING', false)
    }
  },

  // 登出
  async logout({ commit }: any): Promise<void> {
    commit('CLEAR_AUTH')
    removeToken()
  },

  // 获取用户信息 (无认证模式)
  async getUserInfo({ commit }: any): Promise<ApiResponse | null> {
    try {
      const response = await api.get('/auth/me')
      commit('SET_USER', response.data)
      return response
    } catch (error) {
      // 无认证模式：即使失败也不清除认证状态
      console.warn('获取用户信息失败，但在无认证模式下继续:', (error as Error).message)
      return null
    }
  },

  // 刷新Token
  async refreshToken({ commit, state }: any): Promise<string | null> {
    if (!state.token) return null
    
    try {
      const response = await api.post('/auth/refresh', { token: state.token })
      const { token } = response.data
      commit('SET_TOKEN', token)
      setToken(token)
      return token
    } catch (error) {
      commit('CLEAR_AUTH')
      removeToken()
      throw error
    }
  },

  // 更新用户信息
  async updateProfile({ commit }: any, profileData: Partial<User>): Promise<ApiResponse> {
    const response = await api.put('/user/profile', profileData)
    commit('SET_USER', response.data)
    return response
  },

  // 修改密码
  async changePassword({ commit }: any, passwordData: { oldPassword: string; newPassword: string }): Promise<ApiResponse> {
    commit('SET_LOADING', true)
    try {
      const response = await api.put('/user/password', passwordData)
      return response
    } finally {
      commit('SET_LOADING', false)
    }
  }
}

const getters = {
  isAuthenticated: (state: AuthState): boolean => true, // 无认证模式：总是返回已认证
  currentUser: (state: AuthState): User | null => state.user,
  user: (state: AuthState): User | null => state.user,
  loading: (state: AuthState): boolean => state.loading,
  userRole: (state: AuthState): string => state.user?.role || 'ADMIN', // 默认为管理员
  isAdmin: (state: AuthState): boolean => true // 无认证模式：总是返回管理员权限
}

const authModule: Module<AuthState, RootState> = {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}

export default authModule
