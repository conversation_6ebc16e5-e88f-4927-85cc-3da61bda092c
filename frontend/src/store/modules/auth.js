import api from '@/utils/api'
import { removeToken, setToken, getToken } from '@/utils/auth'

const state = {
  token: getToken(),
  user: null,
  loading: false
}

const mutations = {
  SET_TOKEN(state, token) {
    state.token = token
  },
  SET_USER(state, user) {
    state.user = user
  },
  SET_LOADING(state, loading) {
    state.loading = loading
  },
  CLEAR_AUTH(state) {
    state.token = null
    state.user = null
  }
}

const actions = {
  // 登录 (无认证模式)
  async login({ commit }, loginData) {
    commit('SET_LOADING', true)
    try {
      // 直接使用后端 API
      const response = await api.post('/auth/login', loginData)
      const { user } = response.data

      // 无认证模式：直接设置用户信息，不使用Token
      commit('SET_USER', user)
      commit('SET_TOKEN', 'no-auth-mode') // 设置一个标识

      return response
    } finally {
      commit('SET_LOADING', false)
    }
  },

  // 直接设置认证信息（用于快速登录）
  async setAuth({ commit }, { token, user }) {
    commit('SET_TOKEN', token)
    commit('SET_USER', user)
    setToken(token)
    return { token, user }
  },

  // 注册
  async register({ commit }, registerData) {
    commit('SET_LOADING', true)
    try {
      const response = await api.post('/auth/register', registerData)
      return response
    } finally {
      commit('SET_LOADING', false)
    }
  },

  // 发送邮箱验证码
  async sendEmailCode({ commit }, email) {
    const response = await api.post('/auth/send-email-code', null, {
      params: { email }
    })
    return response
  },

  // 检查用户名是否可用
  async checkUsername({ commit }, username) {
    const response = await api.get('/auth/check-username', {
      params: { username }
    })
    return response
  },

  // 检查邮箱是否可用
  async checkEmail({ commit }, email) {
    const response = await api.get('/auth/check-email', {
      params: { email }
    })
    return response
  },

  // 获取用户信息 (无认证模式)
  async getUserInfo({ commit }) {
    try {
      const response = await api.get('/auth/me')
      commit('SET_USER', response.data)
      return response
    } catch (error) {
      // 无认证模式：即使失败也不清除认证状态
      console.warn('获取用户信息失败，但在无认证模式下继续:', error.message)
      return null
    }
  },

  // 登出
  logout({ commit }) {
    commit('CLEAR_AUTH')
    removeToken()
  },

  // 刷新Token
  async refreshToken({ commit, state }) {
    if (!state.token) {
      return
    }
    
    try {
      const response = await api.post('/auth/refresh')
      const { token } = response.data
      
      commit('SET_TOKEN', token)
      setToken(token)
      
      return response
    } catch (error) {
      // 刷新失败，清除认证信息
      commit('CLEAR_AUTH')
      removeToken()
      throw error
    }
  }
}

const getters = {
  isAuthenticated: state => true, // 无认证模式：总是返回已认证
  currentUser: state => state.user,
  user: state => state.user,
  loading: state => state.loading,
  userRole: state => state.user?.role || 'ADMIN', // 默认为管理员
  isAdmin: state => true // 无认证模式：总是返回管理员权限
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
