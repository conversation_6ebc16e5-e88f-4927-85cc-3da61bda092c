import type { Module } from 'vuex'
import type { RootState } from '@/store'

// 定义 app 模块的状态类型
export interface AppState {
  loading: boolean
  sidebarCollapsed: boolean
  theme: 'light' | 'dark'
  language: 'zh-CN' | 'en-US'
}

const state: AppState = {
  loading: false,
  sidebarCollapsed: false,
  theme: 'light',
  language: 'zh-CN'
}

const mutations = {
  SET_LOADING(state: AppState, loading: boolean) {
    state.loading = loading
  },
  TOGGLE_SIDEBAR(state: AppState) {
    state.sidebarCollapsed = !state.sidebarCollapsed
  },
  SET_SIDEBAR_COLLAPSED(state: AppState, collapsed: boolean) {
    state.sidebarCollapsed = collapsed
  },
  SET_THEME(state: AppState, theme: 'light' | 'dark') {
    state.theme = theme
  },
  SET_LANGUAGE(state: AppState, language: 'zh-CN' | 'en-US') {
    state.language = language
  }
}

const actions = {
  setLoading({ commit }: any, loading: boolean) {
    commit('SET_LOADING', loading)
  },
  toggleSidebar({ commit }: any) {
    commit('TOGGLE_SIDEBAR')
  },
  setSidebarCollapsed({ commit }: any, collapsed: boolean) {
    commit('SET_SIDEBAR_COLLAPSED', collapsed)
  },
  setTheme({ commit }: any, theme: 'light' | 'dark') {
    commit('SET_THEME', theme)
    localStorage.setItem('theme', theme)
  },
  setLanguage({ commit }: any, language: 'zh-CN' | 'en-US') {
    commit('SET_LANGUAGE', language)
    localStorage.setItem('language', language)
  },
  initializeApp({ commit }: any) {
    // 从localStorage恢复设置
    const theme = (localStorage.getItem('theme') as 'light' | 'dark') || 'light'
    const language = (localStorage.getItem('language') as 'zh-CN' | 'en-US') || 'zh-CN'
    
    commit('SET_THEME', theme)
    commit('SET_LANGUAGE', language)
  }
}

const getters = {
  loading: (state: AppState): boolean => state.loading,
  sidebarCollapsed: (state: AppState): boolean => state.sidebarCollapsed,
  theme: (state: AppState): 'light' | 'dark' => state.theme,
  language: (state: AppState): 'zh-CN' | 'en-US' => state.language,
  isDarkTheme: (state: AppState): boolean => state.theme === 'dark'
}

const appModule: Module<AppState, RootState> = {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}

export default appModule
