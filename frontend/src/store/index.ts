import { createStore, type Store } from 'vuex'
import auth from './modules/auth'
import app from './modules/app'
import problemset from './modules/problemset'
import problem from './modules/problem'
import user from './modules/user'
import stats from './modules/stats'

// 定义根状态类型
export interface RootState {
  auth: any
  app: any
  problemset: any
  problem: any
  user: any
  stats: any
}

const store: Store<RootState> = createStore({
  modules: {
    auth,
    app,
    problemset,
    problem,
    user,
    stats
  },
  strict: import.meta.env.MODE !== 'production'
})

export default store

// 类型声明
declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $store: Store<RootState>
  }
}
