<template>
  <div id="app">
    <!-- 导航栏 -->
    <Navbar />
    
    <!-- 主要内容区域 -->
    <main class="main-content">
      <router-view />
    </main>
    
    <!-- 页脚 -->
    <Footer />

    <!-- 快速添加按钮 -->
    <QuickAddButton />

    <!-- 全局加载提示 -->
    <div v-if="loading" class="global-loading">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { useStore } from 'vuex'
import Navbar from '@/components/layout/Navbar.vue'
import Footer from '@/components/layout/Footer.vue'
import QuickAddButton from '@/components/common/QuickAddButton.vue'

export default {
  name: 'App',
  components: {
    Navbar,
    Footer,
    QuickAddButton
  },
  setup() {
    const store = useStore()
    
    const loading = computed(() => store.state.app.loading)
    
    return {
      loading
    }
  }
}
</script>

<style>
#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  padding-top: 76px; /* 导航栏高度 */
}

.global-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}
</style>
