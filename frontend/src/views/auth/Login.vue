<template>
  <div class="login-page">
    <div class="container">
      <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
          <div class="card shadow-lg border-0">
            <div class="card-body p-5">
              <!-- 标题 -->
              <div class="text-center mb-4">
                <h2 class="fw-bold text-primary">
                  <i class="bi bi-box-arrow-in-right me-2"></i>
                  用户登录
                </h2>
                <p class="text-muted">欢迎回到 Code-Combined</p>
              </div>

              <!-- 登录表单 -->
              <form @submit.prevent="handleLogin">
                <div class="mb-3">
                  <label for="email" class="form-label">邮箱地址</label>
                  <div class="input-group">
                    <span class="input-group-text">
                      <i class="bi bi-envelope"></i>
                    </span>
                    <input
                      id="email"
                      v-model="form.email"
                      type="email"
                      class="form-control"
                      :class="{ 'is-invalid': errors.email }"
                      placeholder="请输入邮箱地址"
                      required
                    >
                    <div v-if="errors.email" class="invalid-feedback">
                      {{ errors.email }}
                    </div>
                  </div>
                </div>

                <div class="mb-3">
                  <label for="password" class="form-label">密码</label>
                  <div class="input-group">
                    <span class="input-group-text">
                      <i class="bi bi-lock"></i>
                    </span>
                    <input
                      id="password"
                      v-model="form.password"
                      :type="showPassword ? 'text' : 'password'"
                      class="form-control"
                      :class="{ 'is-invalid': errors.password }"
                      placeholder="请输入密码"
                      required
                    >
                    <button
                      type="button"
                      class="btn btn-outline-secondary"
                      @click="showPassword = !showPassword"
                    >
                      <i :class="showPassword ? 'bi bi-eye-slash' : 'bi bi-eye'"></i>
                    </button>
                    <div v-if="errors.password" class="invalid-feedback">
                      {{ errors.password }}
                    </div>
                  </div>
                </div>

                <div class="mb-3 form-check">
                  <input
                    id="rememberMe"
                    v-model="form.rememberMe"
                    type="checkbox"
                    class="form-check-input"
                  >
                  <label for="rememberMe" class="form-check-label">
                    记住我
                  </label>
                </div>

                <div class="d-grid mb-3">
                  <button
                    type="submit"
                    class="btn btn-primary btn-lg"
                    :disabled="loading"
                  >
                    <span v-if="loading" class="spinner-border spinner-border-sm me-2"></span>
                    <i v-else class="bi bi-box-arrow-in-right me-2"></i>
                    {{ loading ? '登录中...' : '登录' }}
                  </button>
                </div>

                <!-- 开发环境快速登录 -->
                <div v-if="isDevelopment" class="mb-3">
                  <hr class="my-3">
                  <p class="text-center text-muted small mb-2">开发环境快速登录</p>
                  <div class="row g-2">
                    <div class="col-6">
                      <button
                        type="button"
                        class="btn btn-danger btn-sm w-100"
                        @click="quickLoginAsAdmin"
                        :disabled="loading"
                      >
                        <i class="bi bi-shield-check me-1"></i>
                        管理员
                      </button>
                    </div>
                    <div class="col-6">
                      <button
                        type="button"
                        class="btn btn-primary btn-sm w-100"
                        @click="quickLoginAsUser"
                        :disabled="loading"
                      >
                        <i class="bi bi-person me-1"></i>
                        普通用户
                      </button>
                    </div>
                  </div>
                </div>

                <div class="text-center">
                  <p class="text-muted mb-0">
                    还没有账号？
                    <router-link to="/register" class="text-decoration-none">
                      立即注册
                    </router-link>
                  </p>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { reactive, ref, computed } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import { showSuccess, showError } from '@/utils/message'

export default {
  name: 'Login',
  setup() {
    const store = useStore()
    const router = useRouter()

    const form = reactive({
      email: '',
      password: '',
      rememberMe: false
    })

    const errors = reactive({})
    const showPassword = ref(false)
    const loading = computed(() => store.getters['auth/loading'])
    const isDevelopment = process.env.NODE_ENV === 'development'

    const validateForm = () => {
      Object.keys(errors).forEach(key => delete errors[key])

      if (!form.email) {
        errors.email = '请输入邮箱地址'
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.email)) {
        errors.email = '邮箱格式不正确'
      }

      if (!form.password) {
        errors.password = '请输入密码'
      } else if (form.password.length < 6) {
        errors.password = '密码长度至少6位'
      }

      return Object.keys(errors).length === 0
    }

    const handleLogin = async () => {
      if (!validateForm()) {
        return
      }

      try {
        await store.dispatch('auth/login', form)

        // 登录成功，跳转到控制台或之前的页面
        const redirect = router.currentRoute.value.query.redirect || '/dashboard'
        router.push(redirect)

        // 显示成功提示
        showSuccess('登录成功！')
      } catch (error) {
        showError(error.message || '登录失败，请检查邮箱和密码')
      }
    }

    // 快速登录为管理员
    const quickLoginAsAdmin = async () => {
      try {
        const { token, user } = quickAdminLogin()
        await store.dispatch('auth/setAuth', { token, user })

        router.push('/dashboard')
        showSuccess('已以管理员身份登录！')
      } catch (error) {
        showError('快速登录失败')
      }
    }

    // 快速登录为普通用户
    const quickLoginAsUser = async () => {
      try {
        const { token, user } = quickUserLogin()
        await store.dispatch('auth/setAuth', { token, user })

        router.push('/dashboard')
        showSuccess('已以普通用户身份登录！')
      } catch (error) {
        showError('快速登录失败')
      }
    }



    return {
      form,
      errors,
      showPassword,
      loading,
      isDevelopment,
      validateForm,
      handleLogin,
      // togglePasswordVisibility,
      quickLoginAsAdmin,
      quickLoginAsUser
    }
  }
}
</script>

<style scoped>
.login-page {
  min-height: calc(100vh - 76px);
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.card {
  border-radius: 1rem;
}

.input-group-text {
  background-color: #f8f9fa;
  border-right: none;
}

.form-control {
  border-left: none;
}

.form-control:focus {
  border-left: none;
  box-shadow: none;
}

.input-group:focus-within .input-group-text {
  border-color: #80bdff;
  background-color: #fff;
}

.btn-outline-secondary {
  border-left: none;
}

@media (max-width: 768px) {
  .card-body {
    padding: 2rem !important;
  }
}
</style>
