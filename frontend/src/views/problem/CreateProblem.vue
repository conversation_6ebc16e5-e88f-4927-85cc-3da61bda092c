<template>
  <div class="create-problem">
    <div class="container py-4">
      <!-- 面包屑导航 -->
      <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
          <li class="breadcrumb-item">
            <router-link to="/" class="text-decoration-none">首页</router-link>
          </li>
          <li class="breadcrumb-item">
            <router-link to="/problems" class="text-decoration-none">题目</router-link>
          </li>
          <li class="breadcrumb-item active" aria-current="page">
            {{ isEdit ? '编辑题目' : '创建题目' }}
          </li>
        </ol>
      </nav>

      <!-- 页面标题 -->
      <div class="row mb-4">
        <div class="col-12">
          <h2>
            <i class="bi bi-plus-circle me-2"></i>
            {{ isEdit ? '编辑题目' : '创建题目' }}
          </h2>
          <p class="text-muted">
            {{ isEdit ? '修改题目信息' : '创建一个新的算法题目，分享你的编程挑战' }}
          </p>
        </div>
      </div>

      <!-- 创建表单 -->
      <div class="row">
        <div class="col-12">
          <div class="card border-0 shadow-sm">
            <div class="card-body p-4">
              <form @submit.prevent="handleSubmit">
                <div class="row">
                  <!-- 左侧：基本信息 -->
                  <div class="col-lg-8">
                    <!-- 题目标题 -->
                    <div class="mb-4">
                      <label for="title" class="form-label fw-medium">
                        题目标题 <span class="text-danger">*</span>
                      </label>
                      <input
                        id="title"
                        v-model="form.title"
                        type="text"
                        class="form-control"
                        :class="{ 'is-invalid': errors.title }"
                        placeholder="请输入题目标题"
                        maxlength="100"
                        required
                      >
                      <div v-if="errors.title" class="invalid-feedback">
                        {{ errors.title }}
                      </div>
                      <div class="form-text">
                        {{ form.title.length }}/100 字符
                      </div>
                    </div>

                    <!-- 题目描述 -->
                    <div class="mb-4">
                      <label for="description" class="form-label fw-medium">
                        题目描述 <span class="text-danger">*</span>
                      </label>
                      <textarea
                        id="description"
                        v-model="form.description"
                        class="form-control"
                        :class="{ 'is-invalid': errors.description }"
                        rows="6"
                        placeholder="请详细描述题目要求、背景和解题思路..."
                        maxlength="2000"
                        required
                      ></textarea>
                      <div v-if="errors.description" class="invalid-feedback">
                        {{ errors.description }}
                      </div>
                      <div class="form-text">
                        {{ form.description.length }}/2000 字符
                      </div>
                    </div>

                    <!-- 输入格式 -->
                    <div class="mb-4">
                      <label for="inputFormat" class="form-label fw-medium">
                        输入格式
                      </label>
                      <textarea
                        id="inputFormat"
                        v-model="form.inputFormat"
                        class="form-control"
                        rows="3"
                        placeholder="描述输入数据的格式和约束..."
                        maxlength="500"
                      ></textarea>
                      <div class="form-text">
                        {{ form.inputFormat.length }}/500 字符
                      </div>
                    </div>

                    <!-- 输出格式 -->
                    <div class="mb-4">
                      <label for="outputFormat" class="form-label fw-medium">
                        输出格式
                      </label>
                      <textarea
                        id="outputFormat"
                        v-model="form.outputFormat"
                        class="form-control"
                        rows="3"
                        placeholder="描述输出数据的格式要求..."
                        maxlength="500"
                      ></textarea>
                      <div class="form-text">
                        {{ form.outputFormat.length }}/500 字符
                      </div>
                    </div>

                    <!-- 示例输入 -->
                    <div class="mb-4">
                      <label for="sampleInput" class="form-label fw-medium">
                        示例输入
                      </label>
                      <textarea
                        id="sampleInput"
                        v-model="form.sampleInput"
                        class="form-control font-monospace"
                        rows="4"
                        placeholder="输入示例数据..."
                        maxlength="1000"
                      ></textarea>
                      <div class="form-text">
                        {{ form.sampleInput.length }}/1000 字符
                      </div>
                    </div>

                    <!-- 示例输出 -->
                    <div class="mb-4">
                      <label for="sampleOutput" class="form-label fw-medium">
                        示例输出
                      </label>
                      <textarea
                        id="sampleOutput"
                        v-model="form.sampleOutput"
                        class="form-control font-monospace"
                        rows="4"
                        placeholder="对应的输出结果..."
                        maxlength="1000"
                      ></textarea>
                      <div class="form-text">
                        {{ form.sampleOutput.length }}/1000 字符
                      </div>
                    </div>

                    <!-- 约束条件 -->
                    <div class="mb-4">
                      <label for="constraints" class="form-label fw-medium">
                        约束条件
                      </label>
                      <textarea
                        id="constraints"
                        v-model="form.constraints"
                        class="form-control"
                        rows="3"
                        placeholder="数据范围、时间复杂度要求等..."
                        maxlength="500"
                      ></textarea>
                      <div class="form-text">
                        {{ form.constraints.length }}/500 字符
                      </div>
                    </div>
                  </div>

                  <!-- 右侧：配置信息 -->
                  <div class="col-lg-4">
                    <!-- 难度设置 -->
                    <div class="mb-4">
                      <label class="form-label fw-medium">
                        题目难度 <span class="text-danger">*</span>
                      </label>
                      <div class="row g-2">
                        <div class="col-12">
                          <div class="form-check">
                            <input
                              id="easy"
                              v-model="form.difficulty"
                              type="radio"
                              value="EASY"
                              class="form-check-input"
                              name="difficulty"
                            >
                            <label for="easy" class="form-check-label">
                              <span class="badge bg-success me-2">简单</span>
                              适合初学者，基础算法
                            </label>
                          </div>
                        </div>
                        <div class="col-12">
                          <div class="form-check">
                            <input
                              id="medium"
                              v-model="form.difficulty"
                              type="radio"
                              value="MEDIUM"
                              class="form-check-input"
                              name="difficulty"
                            >
                            <label for="medium" class="form-check-label">
                              <span class="badge bg-warning me-2">中等</span>
                              需要一定算法基础
                            </label>
                          </div>
                        </div>
                        <div class="col-12">
                          <div class="form-check">
                            <input
                              id="hard"
                              v-model="form.difficulty"
                              type="radio"
                              value="HARD"
                              class="form-check-input"
                              name="difficulty"
                            >
                            <label for="hard" class="form-check-label">
                              <span class="badge bg-danger me-2">困难</span>
                              高级算法，具有挑战性
                            </label>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- 标签 -->
                    <div class="mb-4">
                      <label for="tags" class="form-label fw-medium">
                        题目标签
                      </label>
                      <input
                        id="tags"
                        v-model="form.tags"
                        type="text"
                        class="form-control"
                        placeholder="数组,动态规划,二分查找"
                      >
                      <div class="form-text">
                        用逗号分隔多个标签
                      </div>
                      <div v-if="tagList.length > 0" class="mt-2">
                        <span
                          v-for="tag in tagList"
                          :key="tag"
                          class="badge bg-primary me-1 mb-1"
                        >
                          {{ tag }}
                        </span>
                      </div>
                    </div>

                    <!-- 时间限制 -->
                    <div class="mb-4">
                      <label for="timeLimit" class="form-label fw-medium">
                        时间限制 (ms)
                      </label>
                      <input
                        id="timeLimit"
                        v-model.number="form.timeLimit"
                        type="number"
                        class="form-control"
                        min="100"
                        max="10000"
                        step="100"
                      >
                      <div class="form-text">
                        建议范围：1000-5000ms
                      </div>
                    </div>

                    <!-- 内存限制 -->
                    <div class="mb-4">
                      <label for="memoryLimit" class="form-label fw-medium">
                        内存限制 (MB)
                      </label>
                      <input
                        id="memoryLimit"
                        v-model.number="form.memoryLimit"
                        type="number"
                        class="form-control"
                        min="64"
                        max="1024"
                        step="64"
                      >
                      <div class="form-text">
                        建议范围：128-512MB
                      </div>
                    </div>

                    <!-- 题目来源 -->
                    <div class="mb-4">
                      <label for="source" class="form-label fw-medium">
                        题目来源
                      </label>
                      <select
                        id="source"
                        v-model="form.source"
                        class="form-select"
                      >
                        <option value="自创">自创</option>
                        <option value="LeetCode">LeetCode</option>
                        <option value="牛客网">牛客网</option>
                        <option value="洛谷">洛谷</option>
                        <option value="Codeforces">Codeforces</option>
                        <option value="其他">其他</option>
                      </select>
                    </div>

                    <!-- LeetCode ID -->
                    <div v-if="form.source === 'LeetCode'" class="mb-4">
                      <label for="leetcodeId" class="form-label fw-medium">
                        LeetCode 题号
                      </label>
                      <input
                        id="leetcodeId"
                        v-model="form.leetcodeId"
                        type="text"
                        class="form-control"
                        placeholder="如：1、two-sum"
                      >
                      <div class="form-text">
                        题目编号或英文标识
                      </div>
                    </div>

                    <!-- 添加到题集 -->
                    <div class="mb-4">
                      <label class="form-label fw-medium">
                        添加到题集
                      </label>
                      <div class="form-check">
                        <input
                          id="addToProblemSet"
                          v-model="form.addToProblemSet"
                          type="checkbox"
                          class="form-check-input"
                        >
                        <label for="addToProblemSet" class="form-check-label">
                          创建后添加到指定题集
                        </label>
                      </div>
                      <div v-if="form.addToProblemSet" class="mt-2">
                        <select
                          v-model="form.selectedProblemSetId"
                          class="form-select"
                          :disabled="loadingProblemSets"
                        >
                          <option value="">选择题集</option>
                          <option
                            v-for="problemSet in availableProblemSets"
                            :key="problemSet.id"
                            :value="problemSet.id"
                          >
                            {{ problemSet.name }}
                          </option>
                        </select>
                        <div v-if="loadingProblemSets" class="form-text">
                          <i class="bi bi-hourglass-split me-1"></i>
                          加载题集列表...
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 操作按钮 -->
                <div class="row mt-4">
                  <div class="col-12">
                    <div class="d-flex gap-3 justify-content-end">
                      <router-link to="/problems" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left me-2"></i>
                        取消
                      </router-link>
                      <button
                        type="button"
                        @click="saveDraft"
                        class="btn btn-outline-primary"
                        :disabled="loading"
                      >
                        <i class="bi bi-save me-2"></i>
                        保存草稿
                      </button>
                      <button
                        type="submit"
                        class="btn btn-primary"
                        :disabled="loading || !isFormValid"
                      >
                        <span v-if="loading" class="spinner-border spinner-border-sm me-2"></span>
                        <i v-else class="bi bi-check-circle me-2"></i>
                        {{ isEdit ? '更新题目' : '创建题目' }}
                      </button>
                    </div>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { reactive, computed, onMounted, ref } from 'vue'
import { useStore } from 'vuex'
import { useRouter, useRoute } from 'vue-router'
import { showSuccess, showError } from '@/utils/message'

export default {
  name: 'CreateProblem',
  setup() {
    const store = useStore()
    const router = useRouter()
    const route = useRoute()

    const loadingProblemSets = ref(false)
    const availableProblemSets = ref([])

    const form = reactive({
      title: '',
      description: '',
      difficulty: 'EASY',
      tags: '',
      inputFormat: '',
      outputFormat: '',
      sampleInput: '',
      sampleOutput: '',
      constraints: '',
      timeLimit: 1000,
      memoryLimit: 256,
      source: '自创',
      leetcodeId: '',
      addToProblemSet: false,
      selectedProblemSetId: ''
    })

    const errors = reactive({})
    const loading = computed(() => store.getters['problem/loading'])
    const isEdit = computed(() => !!route.params.id)

    const tagList = computed(() => {
      if (!form.tags) return []
      return form.tags.split(',').map(tag => tag.trim()).filter(tag => tag)
    })

    const isFormValid = computed(() => {
      return form.title.trim().length > 0 &&
             form.description.trim().length > 0 &&
             form.difficulty
    })

    const validateForm = () => {
      Object.keys(errors).forEach(key => delete errors[key])

      if (!form.title.trim()) {
        errors.title = '请输入题目标题'
      } else if (form.title.length > 100) {
        errors.title = '题目标题不能超过100个字符'
      }

      if (!form.description.trim()) {
        errors.description = '请输入题目描述'
      } else if (form.description.length > 2000) {
        errors.description = '题目描述不能超过2000个字符'
      }

      if (!form.difficulty) {
        errors.difficulty = '请选择题目难度'
      }

      return Object.keys(errors).length === 0
    }

    const saveDraft = () => {
      if (!form.title.trim()) {
        showError('请至少输入题目标题')
        return
      }

      // 保存到本地存储
      const draftKey = isEdit.value ? `problem_draft_${route.params.id}` : 'problem_draft_new'
      localStorage.setItem(draftKey, JSON.stringify(form))
      showSuccess('草稿已保存')
    }

    const loadDraft = () => {
      const draftKey = isEdit.value ? `problem_draft_${route.params.id}` : 'problem_draft_new'
      const draft = localStorage.getItem(draftKey)
      if (draft) {
        try {
          const draftData = JSON.parse(draft)
          Object.assign(form, draftData)
        } catch (error) {
          console.error('加载草稿失败:', error)
        }
      }
    }

    const clearDraft = () => {
      const draftKey = isEdit.value ? `problem_draft_${route.params.id}` : 'problem_draft_new'
      localStorage.removeItem(draftKey)
    }

    const loadAvailableProblemSets = async () => {
      loadingProblemSets.value = true
      try {
        // 获取当前用户可编辑的题集
        const response = await store.dispatch('problemset/fetchProblemSets', {
          current: 1,
          size: 100 // 获取更多题集
        })

        // 过滤出当前用户可编辑的题集
        const user = store.getters['auth/user']
        if (user) {
          availableProblemSets.value = store.getters['problemset/problemSets'].filter(ps =>
            user.role === 'ADMIN' || ps.creatorId === user.id
          )
        }
      } catch (error) {
        console.error('获取题集列表失败:', error)
      } finally {
        loadingProblemSets.value = false
      }
    }

    const handleSubmit = async () => {
      if (!validateForm()) return

      try {
        const submitData = {
          title: form.title.trim(),
          description: form.description.trim(),
          difficulty: form.difficulty,
          tags: form.tags.trim() || null,
          inputFormat: form.inputFormat.trim() || null,
          outputFormat: form.outputFormat.trim() || null,
          sampleInput: form.sampleInput.trim() || null,
          sampleOutput: form.sampleOutput.trim() || null,
          constraints: form.constraints.trim() || null,
          timeLimit: form.timeLimit,
          memoryLimit: form.memoryLimit,
          source: form.source,
          leetcodeId: form.leetcodeId.trim() || null
        }

        let result
        if (isEdit.value) {
          result = await store.dispatch('problem/updateProblem', {
            id: route.params.id,
            data: submitData
          })
          showSuccess('题目更新成功')
        } else {
          result = await store.dispatch('problem/createProblem', submitData)
          showSuccess('题目创建成功')

          // 如果选择了添加到题集
          if (form.addToProblemSet && form.selectedProblemSetId) {
            try {
              await addProblemToProblemSet(result.id, form.selectedProblemSetId)
              showSuccess('题目已添加到题集')
            } catch (error) {
              showError('添加到题集失败：' + error.message)
            }
          }
        }

        clearDraft()
        router.push(`/problems/${result.id || route.params.id}`)
      } catch (error) {
        showError(error.message || (isEdit.value ? '更新题目失败' : '创建题目失败'))
      }
    }

    const addProblemToProblemSet = async (problemId, problemSetId) => {
      // 这里应该调用添加题目到题集的API
      // 目前用模拟实现
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve()
        }, 500)
      })
    }

    const loadProblemForEdit = async () => {
      if (!isEdit.value) return

      try {
        const problem = await store.dispatch('problem/fetchProblemById', route.params.id)

        // 检查编辑权限
        const user = store.getters['auth/user']
        if (!user || (user.role !== 'ADMIN' && problem.creatorId !== user.id)) {
          showError('您没有权限编辑此题目')
          router.push('/problems')
          return
        }

        // 填充表单
        form.title = problem.title
        form.description = problem.description
        form.difficulty = problem.difficulty
        form.tags = problem.tags || ''
        form.inputFormat = problem.inputFormat || ''
        form.outputFormat = problem.outputFormat || ''
        form.sampleInput = problem.sampleInput || ''
        form.sampleOutput = problem.sampleOutput || ''
        form.constraints = problem.constraints || ''
        form.timeLimit = problem.timeLimit || 1000
        form.memoryLimit = problem.memoryLimit || 256
        form.source = problem.source || '自创'
        form.leetcodeId = problem.leetcodeId || ''
      } catch (error) {
        showError(error.message || '获取题目信息失败')
        router.push('/problems')
      }
    }

    onMounted(() => {
      if (isEdit.value) {
        loadProblemForEdit()
      } else {
        loadDraft()
      }
      loadAvailableProblemSets()
    })

    return {
      form,
      errors,
      loading,
      isEdit,
      tagList,
      isFormValid,
      loadingProblemSets,
      availableProblemSets,
      saveDraft,
      handleSubmit
    }
  }
}
</script>

<style scoped>
.form-check-label {
  cursor: pointer;
  width: 100%;
}

.form-check {
  padding: 0.75rem;
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
  margin-bottom: 0.5rem;
  transition: all 0.2s ease-in-out;
}

.form-check:hover {
  border-color: #007bff;
  background-color: #f8f9fa;
}

.form-check-input:checked + .form-check-label {
  color: #007bff;
  font-weight: 500;
}

.font-monospace {
  font-family: 'Courier New', Courier, monospace;
  font-size: 0.9rem;
}

.badge {
  font-size: 0.75em;
}

@media (max-width: 768px) {
  .d-flex.gap-3 {
    flex-direction: column;
  }

  .d-flex.gap-3 .btn {
    margin-bottom: 0.5rem;
  }

  .col-lg-4 {
    margin-top: 2rem;
  }
}

/* 表单验证样式 */
.is-invalid {
  border-color: #dc3545;
}

.invalid-feedback {
  display: block;
}

/* 字符计数样式 */
.form-text {
  font-size: 0.875rem;
  color: #6c757d;
}

/* 标签预览样式 */
.badge.bg-primary {
  background-color: #007bff !important;
}
</style>
