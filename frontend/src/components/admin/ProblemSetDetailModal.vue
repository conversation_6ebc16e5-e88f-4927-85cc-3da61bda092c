<template>
  <div class="modal fade show" style="display: block;" tabindex="-1">
    <div class="modal-dialog modal-xl">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">题集详情</h5>
          <button type="button" class="btn-close" @click="$emit('close')"></button>
        </div>
        <div class="modal-body">
          <div class="row">
            <!-- 题集基本信息 -->
            <div class="col-md-4">
              <div class="card">
                <div v-if="problemSet.coverImage" class="card-img-top">
                  <img 
                    :src="problemSet.coverImage" 
                    class="img-fluid"
                    style="height: 200px; object-fit: cover;"
                    :alt="problemSet.name"
                  >
                </div>
                <div class="card-body">
                  <h5 class="card-title">{{ problemSet.name }}</h5>
                  <p class="card-text">{{ problemSet.description }}</p>
                  
                  <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                      <span class="text-muted">可见性：</span>
                      <span 
                        class="badge" 
                        :class="problemSet.isPublic ? 'bg-success' : 'bg-warning'"
                      >
                        {{ problemSet.isPublic ? '公开' : '私有' }}
                      </span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                      <span class="text-muted">状态：</span>
                      <span 
                        class="badge" 
                        :class="getStatusClass(problemSet.status)"
                      >
                        {{ getStatusText(problemSet.status) }}
                      </span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                      <span class="text-muted">难度：</span>
                      <span 
                        class="badge" 
                        :class="getDifficultyClass(problemSet.difficulty)"
                      >
                        {{ getDifficultyText(problemSet.difficulty) }}
                      </span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                      <span class="text-muted">创建者：</span>
                      <span>{{ problemSet.creatorName }}</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                      <span class="text-muted">创建时间：</span>
                      <span>{{ formatDate(problemSet.createdTime) }}</span>
                    </div>
                  </div>

                  <div v-if="problemSet.tags" class="mb-3">
                    <strong>标签：</strong>
                    <div class="mt-2">
                      <span 
                        v-for="tag in problemSet.tags.split(',')" 
                        :key="tag"
                        class="badge bg-light text-dark me-1 mb-1"
                      >
                        {{ tag.trim() }}
                      </span>
                    </div>
                  </div>

                  <div class="row text-center">
                    <div class="col-4">
                      <div class="fw-bold text-primary">{{ problemSet.problemCount || 0 }}</div>
                      <small class="text-muted">题目数</small>
                    </div>
                    <div class="col-4">
                      <div class="fw-bold text-success">{{ problemSet.viewCount || 0 }}</div>
                      <small class="text-muted">浏览量</small>
                    </div>
                    <div class="col-4">
                      <div class="fw-bold text-info">{{ problemSet.likeCount || 0 }}</div>
                      <small class="text-muted">点赞数</small>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 学习路径 -->
              <div v-if="problemSet.learningPath" class="card mt-3">
                <div class="card-header">
                  <h6 class="mb-0">学习路径</h6>
                </div>
                <div class="card-body">
                  <p class="mb-0">{{ problemSet.learningPath }}</p>
                </div>
              </div>
            </div>

            <!-- 题目列表和统计 -->
            <div class="col-md-8">
              <!-- 统计信息 -->
              <div class="card mb-3">
                <div class="card-header">
                  <h6 class="mb-0">统计信息</h6>
                </div>
                <div class="card-body">
                  <div class="row">
                    <div class="col-md-3 text-center">
                      <div class="h4 text-primary">{{ stats.totalProblems }}</div>
                      <small class="text-muted">总题目数</small>
                    </div>
                    <div class="col-md-3 text-center">
                      <div class="h4 text-success">{{ stats.easyProblems }}</div>
                      <small class="text-muted">简单题目</small>
                    </div>
                    <div class="col-md-3 text-center">
                      <div class="h4 text-warning">{{ stats.mediumProblems }}</div>
                      <small class="text-muted">中等题目</small>
                    </div>
                    <div class="col-md-3 text-center">
                      <div class="h4 text-danger">{{ stats.hardProblems }}</div>
                      <small class="text-muted">困难题目</small>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 题目列表 -->
              <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                  <h6 class="mb-0">题目列表</h6>
                  <button class="btn btn-sm btn-primary" @click="manageProblemSetProblems">
                    <i class="bi bi-gear me-1"></i>
                    管理题目
                  </button>
                </div>
                <div class="card-body">
                  <div v-if="problems.length === 0" class="text-center text-muted py-4">
                    <i class="bi bi-inbox fs-1 mb-2"></i>
                    <p>暂无题目</p>
                    <button class="btn btn-outline-primary btn-sm" @click="manageProblemSetProblems">
                      添加题目
                    </button>
                  </div>
                  <div v-else class="table-responsive">
                    <table class="table table-sm">
                      <thead>
                        <tr>
                          <th>序号</th>
                          <th>题目标题</th>
                          <th>难度</th>
                          <th>标签</th>
                          <th>通过率</th>
                          <th>操作</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr v-for="(problem, index) in problems" :key="problem.id">
                          <td>{{ index + 1 }}</td>
                          <td>
                            <a href="#" @click.prevent="viewProblem(problem)" class="text-decoration-none">
                              {{ problem.title }}
                            </a>
                          </td>
                          <td>
                            <span 
                              class="badge" 
                              :class="getProblemDifficultyClass(problem.difficulty)"
                            >
                              {{ getProblemDifficultyText(problem.difficulty) }}
                            </span>
                          </td>
                          <td>
                            <span 
                              v-for="tag in problem.tags.split(',').slice(0, 2)" 
                              :key="tag"
                              class="badge bg-light text-dark me-1"
                            >
                              {{ tag.trim() }}
                            </span>
                          </td>
                          <td>
                            <span class="text-success">
                              {{ calculateAcceptanceRate(problem) }}%
                            </span>
                          </td>
                          <td>
                            <button 
                              class="btn btn-sm btn-outline-primary me-1" 
                              @click="viewProblem(problem)"
                              title="查看详情"
                            >
                              <i class="bi bi-eye"></i>
                            </button>
                            <button 
                              class="btn btn-sm btn-outline-danger" 
                              @click="removeProblemFromSet(problem)"
                              title="从题集中移除"
                            >
                              <i class="bi bi-trash"></i>
                            </button>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" @click="$emit('close')">
            关闭
          </button>
          <button type="button" class="btn btn-primary" @click="editProblemSet">
            编辑题集
          </button>
        </div>
      </div>
    </div>
  </div>
  <div class="modal-backdrop fade show"></div>
</template>

<script>
export default {
  name: 'ProblemSetDetailModal',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    problemSet: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      stats: {
        totalProblems: 0,
        easyProblems: 0,
        mediumProblems: 0,
        hardProblems: 0
      },
      problems: []
    }
  },
  methods: {
    getStatusClass(status) {
      const classes = {
        DRAFT: 'bg-secondary',
        PUBLISHED: 'bg-success',
        ARCHIVED: 'bg-warning'
      }
      return classes[status] || 'bg-secondary'
    },
    getStatusText(status) {
      const texts = {
        DRAFT: '草稿',
        PUBLISHED: '已发布',
        ARCHIVED: '已归档'
      }
      return texts[status] || '未知'
    },
    getDifficultyClass(difficulty) {
      const classes = {
        BEGINNER: 'bg-success',
        INTERMEDIATE: 'bg-warning',
        ADVANCED: 'bg-danger',
        EXPERT: 'bg-dark'
      }
      return classes[difficulty] || 'bg-secondary'
    },
    getDifficultyText(difficulty) {
      const texts = {
        BEGINNER: '初学者',
        INTERMEDIATE: '中级',
        ADVANCED: '高级',
        EXPERT: '专家'
      }
      return texts[difficulty] || '未知'
    },
    getProblemDifficultyClass(difficulty) {
      const classes = {
        EASY: 'bg-success',
        MEDIUM: 'bg-warning',
        HARD: 'bg-danger'
      }
      return classes[difficulty] || 'bg-secondary'
    },
    getProblemDifficultyText(difficulty) {
      const texts = {
        EASY: '简单',
        MEDIUM: '中等',
        HARD: '困难'
      }
      return texts[difficulty] || '未知'
    },
    formatDate(date) {
      if (!date) return '-'
      return new Date(date).toLocaleDateString('zh-CN')
    },
    calculateAcceptanceRate(problem) {
      if (!problem.submissionCount || problem.submissionCount === 0) return 0
      return Math.round((problem.acceptedCount / problem.submissionCount) * 100)
    },
    editProblemSet() {
      this.$emit('close')
      this.$emit('edit', this.problemSet)
    },
    manageProblemSetProblems() {
      this.$emit('close')
      this.$emit('manage-problems', this.problemSet)
    },
    viewProblem(problem) {
      // 触发查看题目详情事件
      this.$emit('view-problem', problem)
    },
    removeProblemFromSet(problem) {
      if (confirm(`确定要从题集中移除题目 "${problem.title}" 吗？`)) {
        this.problems = this.problems.filter(p => p.id !== problem.id)
        this.updateStats()
        // 触发移除事件
        this.$emit('remove-problem', { problemSet: this.problemSet, problem })
      }
    },
    loadProblemSetData() {
      // Mock 题目数据
      this.problems = this.generateMockProblems()
      this.updateStats()
    },
    generateMockProblems() {
      const difficulties = ['EASY', 'MEDIUM', 'HARD']
      const tags = ['数组', '链表', '树', '动态规划', '贪心', '回溯']
      const problems = []
      
      const count = Math.floor(Math.random() * 20) + 5 // 5-25个题目
      
      for (let i = 1; i <= count; i++) {
        const randomTags = tags.sort(() => 0.5 - Math.random()).slice(0, Math.floor(Math.random() * 3) + 1)
        const submissionCount = Math.floor(Math.random() * 1000) + 100
        const acceptedCount = Math.floor(submissionCount * (Math.random() * 0.6 + 0.2)) // 20%-80%通过率
        
        problems.push({
          id: i,
          title: `题目 ${i}`,
          difficulty: difficulties[Math.floor(Math.random() * difficulties.length)],
          tags: randomTags.join(', '),
          submissionCount,
          acceptedCount
        })
      }
      
      return problems
    },
    updateStats() {
      this.stats.totalProblems = this.problems.length
      this.stats.easyProblems = this.problems.filter(p => p.difficulty === 'EASY').length
      this.stats.mediumProblems = this.problems.filter(p => p.difficulty === 'MEDIUM').length
      this.stats.hardProblems = this.problems.filter(p => p.difficulty === 'HARD').length
    }
  },
  mounted() {
    this.loadProblemSetData()
  }
}
</script>

<style scoped>
.modal {
  background-color: rgba(0, 0, 0, 0.5);
}

.btn-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
  color: #000;
  text-shadow: 0 1px 0 #fff;
  opacity: 0.5;
}

.btn-close:hover {
  opacity: 0.75;
}

.card-img-top img {
  width: 100%;
}

.badge {
  font-size: 0.75rem;
}
</style>
