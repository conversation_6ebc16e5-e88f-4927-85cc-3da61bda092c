<template>
  <div class="admin-problems">
    <!-- 页面标题和操作 -->
    <div class="page-header d-flex justify-content-between align-items-center mb-4">
      <div>
        <h2>题目管理</h2>
        <p class="text-muted">管理系统中的所有算法题目</p>
      </div>
      <div>
        <button class="btn btn-primary" @click="showCreateModal = true">
          <i class="bi bi-plus-circle"></i>
          创建题目
        </button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="card shadow mb-4">
      <div class="card-body">
        <div class="row">
          <div class="col-md-3">
            <div class="form-group">
              <label>搜索题目</label>
              <div class="input-group">
                <input 
                  type="text" 
                  class="form-control" 
                  placeholder="题目标题或描述"
                  v-model="searchQuery"
                  @input="handleSearch"
                >
                <div class="input-group-append">
                  <span class="input-group-text">
                    <i class="bi bi-search"></i>
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label>难度筛选</label>
              <select class="form-control" v-model="difficultyFilter" @change="handleFilter">
                <option value="">全部难度</option>
                <option value="EASY">简单</option>
                <option value="MEDIUM">中等</option>
                <option value="HARD">困难</option>
              </select>
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label>状态筛选</label>
              <select class="form-control" v-model="statusFilter" @change="handleFilter">
                <option value="">全部状态</option>
                <option value="DRAFT">草稿</option>
                <option value="PUBLISHED">已发布</option>
                <option value="ARCHIVED">已归档</option>
              </select>
            </div>
          </div>
          <div class="col-md-3">
            <div class="form-group">
              <label>标签筛选</label>
              <select class="form-control" v-model="tagFilter" @change="handleFilter">
                <option value="">全部标签</option>
                <option value="数组">数组</option>
                <option value="链表">链表</option>
                <option value="树">树</option>
                <option value="动态规划">动态规划</option>
                <option value="贪心">贪心</option>
                <option value="回溯">回溯</option>
              </select>
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label>创建者</label>
              <input 
                type="text" 
                class="form-control" 
                placeholder="创建者"
                v-model="creatorFilter"
                @input="handleFilter"
              >
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 题目列表 -->
    <div class="card shadow">
      <div class="card-header py-3">
        <div class="d-flex justify-content-between align-items-center">
          <h6 class="m-0 font-weight-bold text-primary">
            题目列表 ({{ filteredProblems.length }})
          </h6>
          <div class="btn-group" role="group">
            <button class="btn btn-sm btn-outline-secondary" @click="exportProblems">
              <i class="bi bi-download"></i>
              导出
            </button>
            <button class="btn btn-sm btn-outline-secondary" @click="refreshProblems">
              <i class="bi bi-arrow-clockwise"></i>
              刷新
            </button>
          </div>
        </div>
      </div>
      <div class="card-body">
        <div class="table-responsive">
          <table class="table table-bordered table-hover">
            <thead class="thead-light">
              <tr>
                <th>
                  <input type="checkbox" @change="toggleSelectAll" :checked="isAllSelected">
                </th>
                <th>题目信息</th>
                <th>难度</th>
                <th>标签</th>
                <th>状态</th>
                <th>统计</th>
                <th>创建者</th>
                <th>创建时间</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="problem in paginatedProblems" :key="problem.id">
                <td>
                  <input 
                    type="checkbox" 
                    :value="problem.id" 
                    v-model="selectedProblems"
                  >
                </td>
                <td>
                  <div>
                    <div class="fw-bold">{{ problem.title }}</div>
                    <div class="text-muted small">{{ truncateText(problem.description, 50) }}</div>
                  </div>
                </td>
                <td>
                  <span 
                    class="badge" 
                    :class="getDifficultyClass(problem.difficulty)"
                  >
                    {{ getDifficultyText(problem.difficulty) }}
                  </span>
                </td>
                <td>
                  <div class="tags">
                    <span 
                      v-for="tag in problem.tags.split(',')" 
                      :key="tag"
                      class="badge bg-light text-dark me-1"
                    >
                      {{ tag.trim() }}
                    </span>
                  </div>
                </td>
                <td>
                  <span 
                    class="badge" 
                    :class="getStatusClass(problem.status)"
                  >
                    {{ getStatusText(problem.status) }}
                  </span>
                </td>
                <td>
                  <div class="small">
                    <div>浏览: {{ problem.viewCount }}</div>
                    <div>提交: {{ problem.submissionCount }}</div>
                    <div>通过: {{ problem.acceptedCount }}</div>
                  </div>
                </td>
                <td>{{ problem.creatorName }}</td>
                <td>{{ formatDate(problem.createdTime) }}</td>
                <td>
                  <div class="btn-group" role="group">
                    <button 
                      class="btn btn-sm btn-outline-primary" 
                      @click="editProblem(problem)"
                      title="编辑"
                    >
                      <i class="bi bi-pencil"></i>
                    </button>
                    <button 
                      class="btn btn-sm btn-outline-info" 
                      @click="viewProblem(problem)"
                      title="查看详情"
                    >
                      <i class="bi bi-eye"></i>
                    </button>
                    <button 
                      v-if="problem.status === 'DRAFT'"
                      class="btn btn-sm btn-outline-success" 
                      @click="publishProblem(problem)"
                      title="发布"
                    >
                      <i class="bi bi-check-circle"></i>
                    </button>
                    <button 
                      v-if="problem.status === 'PUBLISHED'"
                      class="btn btn-sm btn-outline-warning" 
                      @click="archiveProblem(problem)"
                      title="归档"
                    >
                      <i class="bi bi-archive"></i>
                    </button>
                    <button 
                      class="btn btn-sm btn-outline-danger" 
                      @click="deleteProblem(problem)"
                      title="删除"
                    >
                      <i class="bi bi-trash"></i>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- 分页 -->
        <nav aria-label="题目列表分页">
          <ul class="pagination justify-content-center">
            <li class="page-item" :class="{ disabled: currentPage === 1 }">
              <a class="page-link" @click="changePage(currentPage - 1)">上一页</a>
            </li>
            <li 
              v-for="page in visiblePages" 
              :key="page"
              class="page-item" 
              :class="{ active: page === currentPage }"
            >
              <a class="page-link" @click="changePage(page)">{{ page }}</a>
            </li>
            <li class="page-item" :class="{ disabled: currentPage === totalPages }">
              <a class="page-link" @click="changePage(currentPage + 1)">下一页</a>
            </li>
          </ul>
        </nav>
      </div>
    </div>

    <!-- 批量操作 -->
    <div v-if="selectedProblems.length > 0" class="fixed-bottom-actions">
      <div class="card shadow">
        <div class="card-body py-2">
          <div class="d-flex justify-content-between align-items-center">
            <span>已选择 {{ selectedProblems.length }} 个题目</span>
            <div class="btn-group">
              <button class="btn btn-sm btn-success" @click="batchPublish">
                <i class="bi bi-check-circle"></i>
                批量发布
              </button>
              <button class="btn btn-sm btn-warning" @click="batchArchive">
                <i class="bi bi-archive"></i>
                批量归档
              </button>
              <button class="btn btn-sm btn-danger" @click="batchDelete">
                <i class="bi bi-trash"></i>
                批量删除
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建/编辑题目模态框 -->
    <ProblemModal 
      v-if="showCreateModal || showEditModal"
      :show="showCreateModal || showEditModal"
      :problem="editingProblem"
      :is-edit="showEditModal"
      @close="closeModal"
      @save="saveProblem"
    />

    <!-- 题目详情模态框 -->
    <ProblemDetailModal 
      v-if="showDetailModal"
      :show="showDetailModal"
      :problem="viewingProblem"
      @close="showDetailModal = false"
    />
  </div>
</template>

<script>
import ProblemModal from './ProblemModal.vue'
import ProblemDetailModal from './ProblemDetailModal.vue'

export default {
  name: 'AdminProblems',
  components: {
    ProblemModal,
    ProblemDetailModal
  },
  data() {
    return {
      problems: [],
      searchQuery: '',
      difficultyFilter: '',
      statusFilter: '',
      tagFilter: '',
      creatorFilter: '',
      selectedProblems: [],
      currentPage: 1,
      pageSize: 10,
      showCreateModal: false,
      showEditModal: false,
      showDetailModal: false,
      editingProblem: null,
      viewingProblem: null
    }
  },
  computed: {
    filteredProblems() {
      let filtered = this.problems

      // 搜索过滤
      if (this.searchQuery) {
        const query = this.searchQuery.toLowerCase()
        filtered = filtered.filter(problem => 
          problem.title.toLowerCase().includes(query) ||
          problem.description.toLowerCase().includes(query)
        )
      }

      // 难度过滤
      if (this.difficultyFilter) {
        filtered = filtered.filter(problem => problem.difficulty === this.difficultyFilter)
      }

      // 状态过滤
      if (this.statusFilter) {
        filtered = filtered.filter(problem => problem.status === this.statusFilter)
      }

      // 标签过滤
      if (this.tagFilter) {
        filtered = filtered.filter(problem => problem.tags.includes(this.tagFilter))
      }

      // 创建者过滤
      if (this.creatorFilter) {
        filtered = filtered.filter(problem => 
          problem.creatorName.toLowerCase().includes(this.creatorFilter.toLowerCase())
        )
      }

      return filtered
    },
    paginatedProblems() {
      const start = (this.currentPage - 1) * this.pageSize
      const end = start + this.pageSize
      return this.filteredProblems.slice(start, end)
    },
    totalPages() {
      return Math.ceil(this.filteredProblems.length / this.pageSize)
    },
    visiblePages() {
      const pages = []
      const start = Math.max(1, this.currentPage - 2)
      const end = Math.min(this.totalPages, this.currentPage + 2)
      
      for (let i = start; i <= end; i++) {
        pages.push(i)
      }
      return pages
    },
    isAllSelected() {
      return this.selectedProblems.length === this.paginatedProblems.length && this.paginatedProblems.length > 0
    }
  },
  methods: {
    loadProblems() {
      // Mock 题目数据
      this.problems = this.generateMockProblems(50)
    },
    generateMockProblems(count) {
      const problems = []
      const difficulties = ['EASY', 'MEDIUM', 'HARD']
      const statuses = ['DRAFT', 'PUBLISHED', 'ARCHIVED']
      const tags = ['数组', '链表', '树', '动态规划', '贪心', '回溯', '排序', '搜索']
      const creators = ['张三', '李四', '王五', '赵六', '钱七']
      
      for (let i = 1; i <= count; i++) {
        const randomTags = tags.sort(() => 0.5 - Math.random()).slice(0, Math.floor(Math.random() * 3) + 1)
        problems.push({
          id: i,
          title: `算法题目 ${i}`,
          description: `这是第 ${i} 个算法题目的描述，包含了详细的题目要求和示例说明...`,
          difficulty: difficulties[Math.floor(Math.random() * difficulties.length)],
          tags: randomTags.join(', '),
          status: statuses[Math.floor(Math.random() * statuses.length)],
          viewCount: Math.floor(Math.random() * 1000),
          submissionCount: Math.floor(Math.random() * 500),
          acceptedCount: Math.floor(Math.random() * 200),
          creatorName: creators[Math.floor(Math.random() * creators.length)],
          createdTime: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000)
        })
      }
      return problems
    },
    handleSearch() {
      this.currentPage = 1
    },
    handleFilter() {
      this.currentPage = 1
    },
    changePage(page) {
      if (page >= 1 && page <= this.totalPages) {
        this.currentPage = page
      }
    },
    toggleSelectAll() {
      if (this.isAllSelected) {
        this.selectedProblems = []
      } else {
        this.selectedProblems = this.paginatedProblems.map(problem => problem.id)
      }
    },
    editProblem(problem) {
      this.editingProblem = { ...problem }
      this.showEditModal = true
    },
    viewProblem(problem) {
      this.viewingProblem = problem
      this.showDetailModal = true
    },
    deleteProblem(problem) {
      if (confirm(`确定要删除题目 "${problem.title}" 吗？`)) {
        this.problems = this.problems.filter(p => p.id !== problem.id)
      }
    },
    publishProblem(problem) {
      if (confirm(`确定要发布题目 "${problem.title}" 吗？`)) {
        problem.status = 'PUBLISHED'
      }
    },
    archiveProblem(problem) {
      if (confirm(`确定要归档题目 "${problem.title}" 吗？`)) {
        problem.status = 'ARCHIVED'
      }
    },
    closeModal() {
      this.showCreateModal = false
      this.showEditModal = false
      this.editingProblem = null
    },
    saveProblem(problemData) {
      if (this.showEditModal) {
        // 编辑题目
        const index = this.problems.findIndex(p => p.id === problemData.id)
        if (index !== -1) {
          this.problems[index] = { ...this.problems[index], ...problemData }
        }
      } else {
        // 创建题目
        const newProblem = {
          ...problemData,
          id: Math.max(...this.problems.map(p => p.id)) + 1,
          viewCount: 0,
          submissionCount: 0,
          acceptedCount: 0,
          createdTime: new Date()
        }
        this.problems.unshift(newProblem)
      }
      this.closeModal()
    },
    batchPublish() {
      if (confirm(`确定要发布选中的 ${this.selectedProblems.length} 个题目吗？`)) {
        this.problems.forEach(problem => {
          if (this.selectedProblems.includes(problem.id)) {
            problem.status = 'PUBLISHED'
          }
        })
        this.selectedProblems = []
      }
    },
    batchArchive() {
      if (confirm(`确定要归档选中的 ${this.selectedProblems.length} 个题目吗？`)) {
        this.problems.forEach(problem => {
          if (this.selectedProblems.includes(problem.id)) {
            problem.status = 'ARCHIVED'
          }
        })
        this.selectedProblems = []
      }
    },
    batchDelete() {
      if (confirm(`确定要删除选中的 ${this.selectedProblems.length} 个题目吗？此操作不可恢复！`)) {
        this.problems = this.problems.filter(problem => !this.selectedProblems.includes(problem.id))
        this.selectedProblems = []
      }
    },
    exportProblems() {
      alert('导出功能开发中...')
    },
    refreshProblems() {
      this.loadProblems()
    },
    getDifficultyClass(difficulty) {
      const classes = {
        EASY: 'bg-success',
        MEDIUM: 'bg-warning',
        HARD: 'bg-danger'
      }
      return classes[difficulty] || 'bg-secondary'
    },
    getDifficultyText(difficulty) {
      const texts = {
        EASY: '简单',
        MEDIUM: '中等',
        HARD: '困难'
      }
      return texts[difficulty] || '未知'
    },
    getStatusClass(status) {
      const classes = {
        DRAFT: 'bg-secondary',
        PUBLISHED: 'bg-success',
        ARCHIVED: 'bg-warning'
      }
      return classes[status] || 'bg-secondary'
    },
    getStatusText(status) {
      const texts = {
        DRAFT: '草稿',
        PUBLISHED: '已发布',
        ARCHIVED: '已归档'
      }
      return texts[status] || '未知'
    },
    formatDate(date) {
      if (!date) return '-'
      return new Date(date).toLocaleDateString('zh-CN')
    },
    truncateText(text, length) {
      if (text.length <= length) return text
      return text.substring(0, length) + '...'
    }
  },
  mounted() {
    this.loadProblems()
  }
}
</script>

<style scoped>
.fixed-bottom-actions {
  position: fixed;
  bottom: 0;
  left: 280px;
  right: 0;
  z-index: 1000;
}

@media (max-width: 768px) {
  .fixed-bottom-actions {
    left: 0;
  }
}

.table th {
  border-top: none;
  font-weight: 600;
  background-color: #f8f9fc;
}

.page-link {
  cursor: pointer;
}

.btn-group .btn {
  border-radius: 0.25rem;
  margin-right: 0.25rem;
}

.btn-group .btn:last-child {
  margin-right: 0;
}

.tags {
  max-width: 150px;
}

.tags .badge {
  font-size: 0.7rem;
  margin-bottom: 0.25rem;
}
</style>
