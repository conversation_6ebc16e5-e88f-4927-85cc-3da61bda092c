<template>
  <div class="admin-users">
    <!-- 页面标题和操作 -->
    <div class="page-header d-flex justify-content-between align-items-center mb-4">
      <div>
        <h2>用户管理</h2>
        <p class="text-muted">管理系统中的所有用户账户</p>
      </div>
      <div>
        <button class="btn btn-primary" @click="showCreateModal = true">
          <i class="bi bi-person-plus"></i>
          创建用户
        </button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="card shadow mb-4">
      <div class="card-body">
        <div class="row">
          <div class="col-md-4">
            <div class="form-group">
              <label>搜索用户</label>
              <div class="input-group">
                <input 
                  type="text" 
                  class="form-control" 
                  placeholder="用户名、邮箱或昵称"
                  v-model="searchQuery"
                  @input="handleSearch"
                >
                <div class="input-group-append">
                  <span class="input-group-text">
                    <i class="bi bi-search"></i>
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label>角色筛选</label>
              <select class="form-control" v-model="roleFilter" @change="handleFilter">
                <option value="">全部角色</option>
                <option value="admin">管理员</option>
                <option value="user">普通用户</option>
              </select>
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label>状态筛选</label>
              <select class="form-control" v-model="statusFilter" @change="handleFilter">
                <option value="">全部状态</option>
                <option value="active">活跃</option>
                <option value="inactive">未激活</option>
                <option value="banned">已封禁</option>
              </select>
            </div>
          </div>
          <div class="col-md-4">
            <div class="form-group">
              <label>注册时间</label>
              <div class="row">
                <div class="col-6">
                  <input type="date" class="form-control" v-model="dateFrom">
                </div>
                <div class="col-6">
                  <input type="date" class="form-control" v-model="dateTo">
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 用户列表 -->
    <div class="card shadow">
      <div class="card-header py-3">
        <div class="d-flex justify-content-between align-items-center">
          <h6 class="m-0 font-weight-bold text-primary">
            用户列表 ({{ filteredUsers.length }})
          </h6>
          <div class="btn-group" role="group">
            <button class="btn btn-sm btn-outline-secondary" @click="exportUsers">
              <i class="bi bi-download"></i>
              导出
            </button>
            <button class="btn btn-sm btn-outline-secondary" @click="refreshUsers">
              <i class="bi bi-arrow-clockwise"></i>
              刷新
            </button>
          </div>
        </div>
      </div>
      <div class="card-body">
        <div class="table-responsive">
          <table class="table table-bordered table-hover">
            <thead class="thead-light">
              <tr>
                <th>
                  <input type="checkbox" @change="toggleSelectAll" :checked="isAllSelected">
                </th>
                <th>用户信息</th>
                <th>角色</th>
                <th>状态</th>
                <th>积分</th>
                <th>注册时间</th>
                <th>最后登录</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="user in paginatedUsers" :key="user.id">
                <td>
                  <input 
                    type="checkbox" 
                    :value="user.id" 
                    v-model="selectedUsers"
                  >
                </td>
                <td>
                  <div class="d-flex align-items-center">
                    <img 
                      :src="user.avatar || '/default-avatar.png'" 
                      class="rounded-circle me-3" 
                      width="40" 
                      height="40"
                      :alt="user.nickname"
                    >
                    <div>
                      <div class="fw-bold">{{ user.nickname || user.username }}</div>
                      <div class="text-muted small">{{ user.email }}</div>
                    </div>
                  </div>
                </td>
                <td>
                  <span 
                    class="badge" 
                    :class="user.role === 'admin' ? 'bg-danger' : 'bg-primary'"
                  >
                    {{ user.role === 'admin' ? '管理员' : '普通用户' }}
                  </span>
                </td>
                <td>
                  <span 
                    class="badge" 
                    :class="getStatusClass(user.status)"
                  >
                    {{ getStatusText(user.status) }}
                  </span>
                </td>
                <td>{{ user.points.toLocaleString() }}</td>
                <td>{{ formatDate(user.createdTime) }}</td>
                <td>{{ formatDate(user.lastLoginTime) }}</td>
                <td>
                  <div class="btn-group" role="group">
                    <button 
                      class="btn btn-sm btn-outline-primary" 
                      @click="editUser(user)"
                      title="编辑"
                    >
                      <i class="bi bi-pencil"></i>
                    </button>
                    <button 
                      class="btn btn-sm btn-outline-info" 
                      @click="viewUser(user)"
                      title="查看详情"
                    >
                      <i class="bi bi-eye"></i>
                    </button>
                    <button 
                      class="btn btn-sm" 
                      :class="user.status === 'banned' ? 'btn-outline-success' : 'btn-outline-warning'"
                      @click="toggleUserStatus(user)"
                      :title="user.status === 'banned' ? '解封' : '封禁'"
                    >
                      <i :class="user.status === 'banned' ? 'bi bi-unlock' : 'bi bi-lock'"></i>
                    </button>
                    <button 
                      class="btn btn-sm btn-outline-danger" 
                      @click="deleteUser(user)"
                      title="删除"
                    >
                      <i class="bi bi-trash"></i>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- 分页 -->
        <nav aria-label="用户列表分页">
          <ul class="pagination justify-content-center">
            <li class="page-item" :class="{ disabled: currentPage === 1 }">
              <a class="page-link" @click="changePage(currentPage - 1)">上一页</a>
            </li>
            <li 
              v-for="page in visiblePages" 
              :key="page"
              class="page-item" 
              :class="{ active: page === currentPage }"
            >
              <a class="page-link" @click="changePage(page)">{{ page }}</a>
            </li>
            <li class="page-item" :class="{ disabled: currentPage === totalPages }">
              <a class="page-link" @click="changePage(currentPage + 1)">下一页</a>
            </li>
          </ul>
        </nav>
      </div>
    </div>

    <!-- 批量操作 -->
    <div v-if="selectedUsers.length > 0" class="fixed-bottom-actions">
      <div class="card shadow">
        <div class="card-body py-2">
          <div class="d-flex justify-content-between align-items-center">
            <span>已选择 {{ selectedUsers.length }} 个用户</span>
            <div class="btn-group">
              <button class="btn btn-sm btn-warning" @click="batchBan">
                <i class="bi bi-lock"></i>
                批量封禁
              </button>
              <button class="btn btn-sm btn-success" @click="batchUnban">
                <i class="bi bi-unlock"></i>
                批量解封
              </button>
              <button class="btn btn-sm btn-danger" @click="batchDelete">
                <i class="bi bi-trash"></i>
                批量删除
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建/编辑用户模态框 -->
    <UserModal 
      v-if="showCreateModal || showEditModal"
      :show="showCreateModal || showEditModal"
      :user="editingUser"
      :is-edit="showEditModal"
      @close="closeModal"
      @save="saveUser"
    />

    <!-- 用户详情模态框 -->
    <UserDetailModal 
      v-if="showDetailModal"
      :show="showDetailModal"
      :user="viewingUser"
      @close="showDetailModal = false"
    />
  </div>
</template>

<script>
import UserModal from './UserModal.vue'
import UserDetailModal from './UserDetailModal.vue'

export default {
  name: 'AdminUsers',
  components: {
    UserModal,
    UserDetailModal
  },
  data() {
    return {
      users: [],
      searchQuery: '',
      roleFilter: '',
      statusFilter: '',
      dateFrom: '',
      dateTo: '',
      selectedUsers: [],
      currentPage: 1,
      pageSize: 10,
      showCreateModal: false,
      showEditModal: false,
      showDetailModal: false,
      editingUser: null,
      viewingUser: null
    }
  },
  computed: {
    filteredUsers() {
      let filtered = this.users

      // 搜索过滤
      if (this.searchQuery) {
        const query = this.searchQuery.toLowerCase()
        filtered = filtered.filter(user => 
          user.username.toLowerCase().includes(query) ||
          user.email.toLowerCase().includes(query) ||
          (user.nickname && user.nickname.toLowerCase().includes(query))
        )
      }

      // 角色过滤
      if (this.roleFilter) {
        filtered = filtered.filter(user => user.role === this.roleFilter)
      }

      // 状态过滤
      if (this.statusFilter) {
        filtered = filtered.filter(user => user.status === this.statusFilter)
      }

      return filtered
    },
    paginatedUsers() {
      const start = (this.currentPage - 1) * this.pageSize
      const end = start + this.pageSize
      return this.filteredUsers.slice(start, end)
    },
    totalPages() {
      return Math.ceil(this.filteredUsers.length / this.pageSize)
    },
    visiblePages() {
      const pages = []
      const start = Math.max(1, this.currentPage - 2)
      const end = Math.min(this.totalPages, this.currentPage + 2)
      
      for (let i = start; i <= end; i++) {
        pages.push(i)
      }
      return pages
    },
    isAllSelected() {
      return this.selectedUsers.length === this.paginatedUsers.length && this.paginatedUsers.length > 0
    }
  },
  methods: {
    loadUsers() {
      // Mock 用户数据
      this.users = this.generateMockUsers(100)
    },
    generateMockUsers(count) {
      const users = []
      const roles = ['admin', 'user']
      const statuses = ['active', 'inactive', 'banned']
      
      for (let i = 1; i <= count; i++) {
        users.push({
          id: i,
          username: `user${i}`,
          email: `user${i}@example.com`,
          nickname: `用户${i}`,
          avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${i}`,
          role: i <= 5 ? 'admin' : 'user',
          status: statuses[Math.floor(Math.random() * statuses.length)],
          points: Math.floor(Math.random() * 10000),
          createdTime: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000),
          lastLoginTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000)
        })
      }
      return users
    },
    handleSearch() {
      this.currentPage = 1
    },
    handleFilter() {
      this.currentPage = 1
    },
    changePage(page) {
      if (page >= 1 && page <= this.totalPages) {
        this.currentPage = page
      }
    },
    toggleSelectAll() {
      if (this.isAllSelected) {
        this.selectedUsers = []
      } else {
        this.selectedUsers = this.paginatedUsers.map(user => user.id)
      }
    },
    editUser(user) {
      this.editingUser = { ...user }
      this.showEditModal = true
    },
    viewUser(user) {
      this.viewingUser = user
      this.showDetailModal = true
    },
    deleteUser(user) {
      if (confirm(`确定要删除用户 ${user.nickname || user.username} 吗？`)) {
        this.users = this.users.filter(u => u.id !== user.id)
      }
    },
    toggleUserStatus(user) {
      const action = user.status === 'banned' ? '解封' : '封禁'
      if (confirm(`确定要${action}用户 ${user.nickname || user.username} 吗？`)) {
        user.status = user.status === 'banned' ? 'active' : 'banned'
      }
    },
    closeModal() {
      this.showCreateModal = false
      this.showEditModal = false
      this.editingUser = null
    },
    saveUser(userData) {
      if (this.showEditModal) {
        // 编辑用户
        const index = this.users.findIndex(u => u.id === userData.id)
        if (index !== -1) {
          this.users[index] = { ...this.users[index], ...userData }
        }
      } else {
        // 创建用户
        const newUser = {
          ...userData,
          id: Math.max(...this.users.map(u => u.id)) + 1,
          createdTime: new Date(),
          lastLoginTime: null
        }
        this.users.unshift(newUser)
      }
      this.closeModal()
    },
    batchBan() {
      if (confirm(`确定要封禁选中的 ${this.selectedUsers.length} 个用户吗？`)) {
        this.users.forEach(user => {
          if (this.selectedUsers.includes(user.id)) {
            user.status = 'banned'
          }
        })
        this.selectedUsers = []
      }
    },
    batchUnban() {
      if (confirm(`确定要解封选中的 ${this.selectedUsers.length} 个用户吗？`)) {
        this.users.forEach(user => {
          if (this.selectedUsers.includes(user.id)) {
            user.status = 'active'
          }
        })
        this.selectedUsers = []
      }
    },
    batchDelete() {
      if (confirm(`确定要删除选中的 ${this.selectedUsers.length} 个用户吗？此操作不可恢复！`)) {
        this.users = this.users.filter(user => !this.selectedUsers.includes(user.id))
        this.selectedUsers = []
      }
    },
    exportUsers() {
      alert('导出功能开发中...')
    },
    refreshUsers() {
      this.loadUsers()
    },
    getStatusClass(status) {
      const classes = {
        active: 'bg-success',
        inactive: 'bg-warning',
        banned: 'bg-danger'
      }
      return classes[status] || 'bg-secondary'
    },
    getStatusText(status) {
      const texts = {
        active: '活跃',
        inactive: '未激活',
        banned: '已封禁'
      }
      return texts[status] || '未知'
    },
    formatDate(date) {
      if (!date) return '-'
      return new Date(date).toLocaleDateString('zh-CN')
    }
  },
  mounted() {
    this.loadUsers()
  }
}
</script>

<style scoped>
.fixed-bottom-actions {
  position: fixed;
  bottom: 0;
  left: 280px;
  right: 0;
  z-index: 1000;
}

@media (max-width: 768px) {
  .fixed-bottom-actions {
    left: 0;
  }
}

.table th {
  border-top: none;
  font-weight: 600;
  background-color: #f8f9fc;
}

.page-link {
  cursor: pointer;
}

.btn-group .btn {
  border-radius: 0.25rem;
  margin-right: 0.25rem;
}

.btn-group .btn:last-child {
  margin-right: 0;
}
</style>
