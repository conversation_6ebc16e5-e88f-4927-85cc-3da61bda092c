<template>
  <div class="admin-problemsets">
    <!-- 页面标题和操作 -->
    <div class="page-header d-flex justify-content-between align-items-center mb-4">
      <div>
        <h2>题集管理</h2>
        <p class="text-muted">管理系统中的所有题目集合</p>
      </div>
      <div>
        <button class="btn btn-primary" @click="showCreateModal = true">
          <i class="bi bi-collection-fill"></i>
          创建题集
        </button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="card shadow mb-4">
      <div class="card-body">
        <div class="row">
          <div class="col-md-4">
            <div class="form-group">
              <label>搜索题集</label>
              <div class="input-group">
                <input 
                  type="text" 
                  class="form-control" 
                  placeholder="题集名称或描述"
                  v-model="searchQuery"
                  @input="handleSearch"
                >
                <div class="input-group-append">
                  <span class="input-group-text">
                    <i class="bi bi-search"></i>
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label>可见性</label>
              <select class="form-control" v-model="visibilityFilter" @change="handleFilter">
                <option value="">全部</option>
                <option value="true">公开</option>
                <option value="false">私有</option>
              </select>
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label>状态筛选</label>
              <select class="form-control" v-model="statusFilter" @change="handleFilter">
                <option value="">全部状态</option>
                <option value="DRAFT">草稿</option>
                <option value="PUBLISHED">已发布</option>
                <option value="ARCHIVED">已归档</option>
              </select>
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label>题目数量</label>
              <select class="form-control" v-model="problemCountFilter" @change="handleFilter">
                <option value="">全部</option>
                <option value="0">空题集</option>
                <option value="1-10">1-10题</option>
                <option value="11-50">11-50题</option>
                <option value="50+">50题以上</option>
              </select>
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label>创建者</label>
              <input 
                type="text" 
                class="form-control" 
                placeholder="创建者"
                v-model="creatorFilter"
                @input="handleFilter"
              >
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 题集列表 -->
    <div class="card shadow">
      <div class="card-header py-3">
        <div class="d-flex justify-content-between align-items-center">
          <h6 class="m-0 font-weight-bold text-primary">
            题集列表 ({{ filteredProblemSets.length }})
          </h6>
          <div class="btn-group" role="group">
            <button class="btn btn-sm btn-outline-secondary" @click="exportProblemSets">
              <i class="bi bi-download"></i>
              导出
            </button>
            <button class="btn btn-sm btn-outline-secondary" @click="refreshProblemSets">
              <i class="bi bi-arrow-clockwise"></i>
              刷新
            </button>
          </div>
        </div>
      </div>
      <div class="card-body">
        <div class="table-responsive">
          <table class="table table-bordered table-hover">
            <thead class="thead-light">
              <tr>
                <th>
                  <input type="checkbox" @change="toggleSelectAll" :checked="isAllSelected">
                </th>
                <th>题集信息</th>
                <th>可见性</th>
                <th>状态</th>
                <th>题目数量</th>
                <th>统计</th>
                <th>创建者</th>
                <th>创建时间</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="problemSet in paginatedProblemSets" :key="problemSet.id">
                <td>
                  <input 
                    type="checkbox" 
                    :value="problemSet.id" 
                    v-model="selectedProblemSets"
                  >
                </td>
                <td>
                  <div class="d-flex align-items-center">
                    <div class="problemset-icon me-3">
                      <i class="bi bi-collection text-primary"></i>
                    </div>
                    <div>
                      <div class="fw-bold">{{ problemSet.name }}</div>
                      <div class="text-muted small">{{ truncateText(problemSet.description, 60) }}</div>
                    </div>
                  </div>
                </td>
                <td>
                  <span 
                    class="badge" 
                    :class="problemSet.isPublic ? 'bg-success' : 'bg-warning'"
                  >
                    {{ problemSet.isPublic ? '公开' : '私有' }}
                  </span>
                </td>
                <td>
                  <span 
                    class="badge" 
                    :class="getStatusClass(problemSet.status)"
                  >
                    {{ getStatusText(problemSet.status) }}
                  </span>
                </td>
                <td>
                  <span class="badge bg-info">{{ problemSet.problemCount }} 题</span>
                </td>
                <td>
                  <div class="small">
                    <div>浏览: {{ problemSet.viewCount }}</div>
                    <div>点赞: {{ problemSet.likeCount }}</div>
                    <div>收藏: {{ problemSet.favoriteCount || 0 }}</div>
                  </div>
                </td>
                <td>{{ problemSet.creatorName }}</td>
                <td>{{ formatDate(problemSet.createdTime) }}</td>
                <td>
                  <div class="btn-group" role="group">
                    <button 
                      class="btn btn-sm btn-outline-primary" 
                      @click="editProblemSet(problemSet)"
                      title="编辑"
                    >
                      <i class="bi bi-pencil"></i>
                    </button>
                    <button 
                      class="btn btn-sm btn-outline-info" 
                      @click="viewProblemSet(problemSet)"
                      title="查看详情"
                    >
                      <i class="bi bi-eye"></i>
                    </button>
                    <button 
                      class="btn btn-sm btn-outline-secondary" 
                      @click="manageProblemSetProblems(problemSet)"
                      title="管理题目"
                    >
                      <i class="bi bi-list-ul"></i>
                    </button>
                    <button 
                      v-if="problemSet.status === 'DRAFT'"
                      class="btn btn-sm btn-outline-success" 
                      @click="publishProblemSet(problemSet)"
                      title="发布"
                    >
                      <i class="bi bi-check-circle"></i>
                    </button>
                    <button 
                      v-if="problemSet.status === 'PUBLISHED'"
                      class="btn btn-sm btn-outline-warning" 
                      @click="archiveProblemSet(problemSet)"
                      title="归档"
                    >
                      <i class="bi bi-archive"></i>
                    </button>
                    <button 
                      class="btn btn-sm btn-outline-danger" 
                      @click="deleteProblemSet(problemSet)"
                      title="删除"
                    >
                      <i class="bi bi-trash"></i>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- 分页 -->
        <nav aria-label="题集列表分页">
          <ul class="pagination justify-content-center">
            <li class="page-item" :class="{ disabled: currentPage === 1 }">
              <a class="page-link" @click="changePage(currentPage - 1)">上一页</a>
            </li>
            <li 
              v-for="page in visiblePages" 
              :key="page"
              class="page-item" 
              :class="{ active: page === currentPage }"
            >
              <a class="page-link" @click="changePage(page)">{{ page }}</a>
            </li>
            <li class="page-item" :class="{ disabled: currentPage === totalPages }">
              <a class="page-link" @click="changePage(currentPage + 1)">下一页</a>
            </li>
          </ul>
        </nav>
      </div>
    </div>

    <!-- 批量操作 -->
    <div v-if="selectedProblemSets.length > 0" class="fixed-bottom-actions">
      <div class="card shadow">
        <div class="card-body py-2">
          <div class="d-flex justify-content-between align-items-center">
            <span>已选择 {{ selectedProblemSets.length }} 个题集</span>
            <div class="btn-group">
              <button class="btn btn-sm btn-success" @click="batchPublish">
                <i class="bi bi-check-circle"></i>
                批量发布
              </button>
              <button class="btn btn-sm btn-warning" @click="batchArchive">
                <i class="bi bi-archive"></i>
                批量归档
              </button>
              <button class="btn btn-sm btn-info" @click="batchSetPublic">
                <i class="bi bi-unlock"></i>
                设为公开
              </button>
              <button class="btn btn-sm btn-secondary" @click="batchSetPrivate">
                <i class="bi bi-lock"></i>
                设为私有
              </button>
              <button class="btn btn-sm btn-danger" @click="batchDelete">
                <i class="bi bi-trash"></i>
                批量删除
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建/编辑题集模态框 -->
    <ProblemSetModal 
      v-if="showCreateModal || showEditModal"
      :show="showCreateModal || showEditModal"
      :problemSet="editingProblemSet"
      :is-edit="showEditModal"
      @close="closeModal"
      @save="saveProblemSet"
    />

    <!-- 题集详情模态框 -->
    <ProblemSetDetailModal 
      v-if="showDetailModal"
      :show="showDetailModal"
      :problemSet="viewingProblemSet"
      @close="showDetailModal = false"
    />

    <!-- 题集题目管理模态框 -->
    <ProblemSetProblemsModal 
      v-if="showProblemsModal"
      :show="showProblemsModal"
      :problemSet="managingProblemSet"
      @close="showProblemsModal = false"
    />
  </div>
</template>

<script>
import ProblemSetModal from './ProblemSetModal.vue'
import ProblemSetDetailModal from './ProblemSetDetailModal.vue'
import ProblemSetProblemsModal from './ProblemSetProblemsModal.vue'

export default {
  name: 'AdminProblemSets',
  components: {
    ProblemSetModal,
    ProblemSetDetailModal,
    ProblemSetProblemsModal
  },
  data() {
    return {
      problemSets: [],
      searchQuery: '',
      visibilityFilter: '',
      statusFilter: '',
      problemCountFilter: '',
      creatorFilter: '',
      selectedProblemSets: [],
      currentPage: 1,
      pageSize: 10,
      showCreateModal: false,
      showEditModal: false,
      showDetailModal: false,
      showProblemsModal: false,
      editingProblemSet: null,
      viewingProblemSet: null,
      managingProblemSet: null
    }
  },
  computed: {
    filteredProblemSets() {
      let filtered = this.problemSets

      // 搜索过滤
      if (this.searchQuery) {
        const query = this.searchQuery.toLowerCase()
        filtered = filtered.filter(problemSet => 
          problemSet.name.toLowerCase().includes(query) ||
          problemSet.description.toLowerCase().includes(query)
        )
      }

      // 可见性过滤
      if (this.visibilityFilter !== '') {
        filtered = filtered.filter(problemSet => 
          problemSet.isPublic === (this.visibilityFilter === 'true')
        )
      }

      // 状态过滤
      if (this.statusFilter) {
        filtered = filtered.filter(problemSet => problemSet.status === this.statusFilter)
      }

      // 题目数量过滤
      if (this.problemCountFilter) {
        filtered = filtered.filter(problemSet => {
          const count = problemSet.problemCount
          switch (this.problemCountFilter) {
            case '0': return count === 0
            case '1-10': return count >= 1 && count <= 10
            case '11-50': return count >= 11 && count <= 50
            case '50+': return count > 50
            default: return true
          }
        })
      }

      // 创建者过滤
      if (this.creatorFilter) {
        filtered = filtered.filter(problemSet => 
          problemSet.creatorName.toLowerCase().includes(this.creatorFilter.toLowerCase())
        )
      }

      return filtered
    },
    paginatedProblemSets() {
      const start = (this.currentPage - 1) * this.pageSize
      const end = start + this.pageSize
      return this.filteredProblemSets.slice(start, end)
    },
    totalPages() {
      return Math.ceil(this.filteredProblemSets.length / this.pageSize)
    },
    visiblePages() {
      const pages = []
      const start = Math.max(1, this.currentPage - 2)
      const end = Math.min(this.totalPages, this.currentPage + 2)
      
      for (let i = start; i <= end; i++) {
        pages.push(i)
      }
      return pages
    },
    isAllSelected() {
      return this.selectedProblemSets.length === this.paginatedProblemSets.length && this.paginatedProblemSets.length > 0
    }
  },
  methods: {
    loadProblemSets() {
      // 加载题集数据
      // TODO: 调用后端API获取真实题集数据
    },
    generateMockProblemSets(count) {
      const problemSets = []
      const statuses = ['DRAFT', 'PUBLISHED', 'ARCHIVED']
      const creators = ['张三', '李四', '王五', '赵六', '钱七']
      const names = [
        '算法基础入门', '数据结构精讲', '动态规划专题', '贪心算法集合', 
        '图论算法', '字符串处理', '数学算法', '排序算法大全',
        '搜索算法专题', '树形结构', '链表操作', '栈和队列'
      ]
      
      for (let i = 1; i <= count; i++) {
        problemSets.push({
          id: i,
          name: names[Math.floor(Math.random() * names.length)] + ` ${i}`,
          description: `这是第 ${i} 个题集的描述，包含了相关算法题目的详细说明和学习路径...`,
          isPublic: Math.random() > 0.3,
          status: statuses[Math.floor(Math.random() * statuses.length)],
          problemCount: Math.floor(Math.random() * 100),
          viewCount: Math.floor(Math.random() * 5000),
          likeCount: Math.floor(Math.random() * 500),
          favoriteCount: Math.floor(Math.random() * 200),
          creatorName: creators[Math.floor(Math.random() * creators.length)],
          createdTime: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000)
        })
      }
      return problemSets
    },
    handleSearch() {
      this.currentPage = 1
    },
    handleFilter() {
      this.currentPage = 1
    },
    changePage(page) {
      if (page >= 1 && page <= this.totalPages) {
        this.currentPage = page
      }
    },
    toggleSelectAll() {
      if (this.isAllSelected) {
        this.selectedProblemSets = []
      } else {
        this.selectedProblemSets = this.paginatedProblemSets.map(problemSet => problemSet.id)
      }
    },
    editProblemSet(problemSet) {
      this.editingProblemSet = { ...problemSet }
      this.showEditModal = true
    },
    viewProblemSet(problemSet) {
      this.viewingProblemSet = problemSet
      this.showDetailModal = true
    },
    manageProblemSetProblems(problemSet) {
      this.managingProblemSet = problemSet
      this.showProblemsModal = true
    },
    deleteProblemSet(problemSet) {
      if (confirm(`确定要删除题集 "${problemSet.name}" 吗？`)) {
        this.problemSets = this.problemSets.filter(ps => ps.id !== problemSet.id)
      }
    },
    publishProblemSet(problemSet) {
      if (confirm(`确定要发布题集 "${problemSet.name}" 吗？`)) {
        problemSet.status = 'PUBLISHED'
      }
    },
    archiveProblemSet(problemSet) {
      if (confirm(`确定要归档题集 "${problemSet.name}" 吗？`)) {
        problemSet.status = 'ARCHIVED'
      }
    },
    closeModal() {
      this.showCreateModal = false
      this.showEditModal = false
      this.editingProblemSet = null
    },
    saveProblemSet(problemSetData) {
      if (this.showEditModal) {
        // 编辑题集
        const index = this.problemSets.findIndex(ps => ps.id === problemSetData.id)
        if (index !== -1) {
          this.problemSets[index] = { ...this.problemSets[index], ...problemSetData }
        }
      } else {
        // 创建题集
        const newProblemSet = {
          ...problemSetData,
          id: Math.max(...this.problemSets.map(ps => ps.id)) + 1,
          problemCount: 0,
          viewCount: 0,
          likeCount: 0,
          favoriteCount: 0,
          createdTime: new Date()
        }
        this.problemSets.unshift(newProblemSet)
      }
      this.closeModal()
    },
    batchPublish() {
      if (confirm(`确定要发布选中的 ${this.selectedProblemSets.length} 个题集吗？`)) {
        this.problemSets.forEach(problemSet => {
          if (this.selectedProblemSets.includes(problemSet.id)) {
            problemSet.status = 'PUBLISHED'
          }
        })
        this.selectedProblemSets = []
      }
    },
    batchArchive() {
      if (confirm(`确定要归档选中的 ${this.selectedProblemSets.length} 个题集吗？`)) {
        this.problemSets.forEach(problemSet => {
          if (this.selectedProblemSets.includes(problemSet.id)) {
            problemSet.status = 'ARCHIVED'
          }
        })
        this.selectedProblemSets = []
      }
    },
    batchSetPublic() {
      if (confirm(`确定要将选中的 ${this.selectedProblemSets.length} 个题集设为公开吗？`)) {
        this.problemSets.forEach(problemSet => {
          if (this.selectedProblemSets.includes(problemSet.id)) {
            problemSet.isPublic = true
          }
        })
        this.selectedProblemSets = []
      }
    },
    batchSetPrivate() {
      if (confirm(`确定要将选中的 ${this.selectedProblemSets.length} 个题集设为私有吗？`)) {
        this.problemSets.forEach(problemSet => {
          if (this.selectedProblemSets.includes(problemSet.id)) {
            problemSet.isPublic = false
          }
        })
        this.selectedProblemSets = []
      }
    },
    batchDelete() {
      if (confirm(`确定要删除选中的 ${this.selectedProblemSets.length} 个题集吗？此操作不可恢复！`)) {
        this.problemSets = this.problemSets.filter(problemSet => !this.selectedProblemSets.includes(problemSet.id))
        this.selectedProblemSets = []
      }
    },
    exportProblemSets() {
      alert('导出功能开发中...')
    },
    refreshProblemSets() {
      this.loadProblemSets()
    },
    getStatusClass(status) {
      const classes = {
        DRAFT: 'bg-secondary',
        PUBLISHED: 'bg-success',
        ARCHIVED: 'bg-warning'
      }
      return classes[status] || 'bg-secondary'
    },
    getStatusText(status) {
      const texts = {
        DRAFT: '草稿',
        PUBLISHED: '已发布',
        ARCHIVED: '已归档'
      }
      return texts[status] || '未知'
    },
    formatDate(date) {
      if (!date) return '-'
      return new Date(date).toLocaleDateString('zh-CN')
    },
    truncateText(text, length) {
      if (text.length <= length) return text
      return text.substring(0, length) + '...'
    }
  },
  mounted() {
    this.loadProblemSets()
  }
}
</script>

<style scoped>
.fixed-bottom-actions {
  position: fixed;
  bottom: 0;
  left: 280px;
  right: 0;
  z-index: 1000;
}

@media (max-width: 768px) {
  .fixed-bottom-actions {
    left: 0;
  }
}

.table th {
  border-top: none;
  font-weight: 600;
  background-color: #f8f9fc;
}

.page-link {
  cursor: pointer;
}

.btn-group .btn {
  border-radius: 0.25rem;
  margin-right: 0.25rem;
}

.btn-group .btn:last-child {
  margin-right: 0;
}

.problemset-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fc;
  border-radius: 8px;
}

.problemset-icon i {
  font-size: 1.5rem;
}
</style>
