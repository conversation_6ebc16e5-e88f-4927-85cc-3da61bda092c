<template>
  <!-- 添加题目到题集模态框 -->
  <div 
    class="modal fade" 
    id="addProblemModal" 
    tabindex="-1" 
    aria-labelledby="addProblemModalLabel" 
    aria-hidden="true"
  >
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="addProblemModalLabel">
            <i class="bi bi-plus-circle me-2"></i>
            添加题目到题集
          </h5>
          <button 
            type="button" 
            class="btn-close" 
            data-bs-dismiss="modal" 
            aria-label="Close"
          ></button>
        </div>
        
        <div class="modal-body">
          <!-- 搜索框 -->
          <div class="mb-3">
            <div class="input-group">
              <span class="input-group-text">
                <i class="bi bi-search"></i>
              </span>
              <input
                v-model="searchKeyword"
                type="text"
                class="form-control"
                placeholder="搜索题目标题、描述或标签..."
                @input="handleSearch"
                @keyup.enter="handleQuickAdd"
              >
              <button
                v-if="searchKeyword"
                @click="clearSearch"
                class="btn btn-outline-secondary"
                type="button"
              >
                <i class="bi bi-x"></i>
              </button>
            </div>
            <div class="form-text">
              支持搜索题目标题、描述内容和标签，按回车键快速添加第一个结果
            </div>
          </div>

          <!-- 搜索建议 -->
          <div v-if="searchSuggestions.length > 0 && searchKeyword" class="mb-3">
            <div class="search-suggestions">
              <small class="text-muted">搜索建议：</small>
              <div class="mt-1">
                <span
                  v-for="suggestion in searchSuggestions.slice(0, 5)"
                  :key="suggestion"
                  @click="applySuggestion(suggestion)"
                  class="badge bg-light text-dark me-1 mb-1 suggestion-tag"
                >
                  {{ suggestion }}
                </span>
              </div>
            </div>
          </div>

          <!-- 难度筛选 -->
          <div class="mb-3">
            <div class="btn-group" role="group">
              <input 
                id="all-difficulty" 
                v-model="selectedDifficulty" 
                type="radio" 
                value="" 
                class="btn-check"
              >
              <label for="all-difficulty" class="btn btn-outline-secondary btn-sm">
                全部
              </label>
              
              <input 
                id="easy-difficulty" 
                v-model="selectedDifficulty" 
                type="radio" 
                value="EASY" 
                class="btn-check"
              >
              <label for="easy-difficulty" class="btn btn-outline-success btn-sm">
                简单
              </label>
              
              <input 
                id="medium-difficulty" 
                v-model="selectedDifficulty" 
                type="radio" 
                value="MEDIUM" 
                class="btn-check"
              >
              <label for="medium-difficulty" class="btn btn-outline-warning btn-sm">
                中等
              </label>
              
              <input 
                id="hard-difficulty" 
                v-model="selectedDifficulty" 
                type="radio" 
                value="HARD" 
                class="btn-check"
              >
              <label for="hard-difficulty" class="btn btn-outline-danger btn-sm">
                困难
              </label>
            </div>
          </div>

          <!-- 题目列表 -->
          <div v-if="loading" class="text-center py-3">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2 text-muted">加载题目列表...</p>
          </div>

          <div v-else-if="availableProblems.length === 0" class="text-center py-3">
            <i class="bi bi-puzzle text-muted" style="font-size: 3rem;"></i>
            <h6 class="mt-3 text-muted">暂无可用题目</h6>
            <p class="text-muted">
              {{ searchKeyword ? '没有找到匹配的题目' : '还没有创建任何题目' }}
            </p>
            <router-link to="/problems/create" class="btn btn-primary btn-sm">
              <i class="bi bi-plus-circle me-1"></i>
              创建题目
            </router-link>
          </div>

          <div v-else>
            <!-- 搜索结果统计 -->
            <div class="search-stats mb-3">
              <div class="d-flex justify-content-between align-items-center">
                <small class="text-muted">
                  <i class="bi bi-funnel me-1"></i>
                  找到 {{ availableProblems.length }} 个题目
                  <span v-if="searchKeyword">（搜索："{{ searchKeyword }}"）</span>
                </small>
                <div class="d-flex gap-2">
                  <button
                    v-if="availableProblems.length > 0"
                    @click="selectAll"
                    class="btn btn-outline-primary btn-sm"
                  >
                    <i class="bi bi-check-all me-1"></i>
                    全选
                  </button>
                  <button
                    v-if="selectedProblems.length > 0"
                    @click="clearSelection"
                    class="btn btn-outline-secondary btn-sm"
                  >
                    <i class="bi bi-x-circle me-1"></i>
                    清空选择
                  </button>
                </div>
              </div>
            </div>

            <div class="problem-list" style="max-height: 400px; overflow-y: auto;">
              <div
                v-for="(problem, index) in availableProblems"
                :key="problem.id"
                class="problem-item mb-2"
                :class="{ 'selected': selectedProblems.includes(problem.id) }"
              >
                <div class="form-check">
                  <input
                    :id="`problem_${problem.id}`"
                    v-model="selectedProblems"
                    type="checkbox"
                    :value="problem.id"
                    class="form-check-input"
                  >
                  <label :for="`problem_${problem.id}`" class="form-check-label">
                    <div class="d-flex justify-content-between align-items-start">
                      <div class="flex-grow-1">
                        <div class="d-flex align-items-center gap-2 mb-1">
                          <span class="problem-index">#{{ index + 1 }}</span>
                          <div class="fw-medium problem-title" v-html="highlightSearchTerm(problem.title)"></div>
                        </div>
                        <div class="problem-description text-muted small mb-2" v-html="highlightSearchTerm(problem.description)"></div>
                        <div class="problem-meta d-flex align-items-center gap-2">
                          <span
                            :class="getDifficultyClass(problem.difficulty)"
                            class="badge"
                          >
                            {{ getDifficultyText(problem.difficulty) }}
                          </span>
                          <div class="problem-tags">
                            <span
                              v-for="tag in getTagList(problem.tags).slice(0, 4)"
                              :key="tag"
                              class="badge bg-light text-dark me-1"
                              v-html="highlightSearchTerm(tag)"
                            >
                            </span>
                            <span
                              v-if="getTagList(problem.tags).length > 4"
                              class="badge bg-light text-muted"
                            >
                              +{{ getTagList(problem.tags).length - 4 }}
                            </span>
                          </div>
                        </div>
                      </div>
                      <div class="problem-actions">
                        <button
                          @click.stop="quickAddSingle(problem)"
                          class="btn btn-outline-success btn-sm"
                          title="快速添加此题目"
                        >
                          <i class="bi bi-plus"></i>
                        </button>
                      </div>
                    </div>
                  </label>
                </div>
              </div>
            </div>

            <!-- 选择状态和批量操作 -->
            <div v-if="selectedProblems.length > 0" class="selected-summary mt-3">
              <div class="alert alert-info d-flex justify-content-between align-items-center">
                <div>
                  <i class="bi bi-check-circle me-2"></i>
                  已选择 {{ selectedProblems.length }} 个题目
                </div>
                <button
                  @click="handleAddProblems"
                  class="btn btn-primary btn-sm"
                  :disabled="submitting"
                >
                  <span v-if="submitting" class="spinner-border spinner-border-sm me-2"></span>
                  <i v-else class="bi bi-plus-circle me-2"></i>
                  {{ submitting ? '添加中...' : `添加 ${selectedProblems.length} 个题目` }}
                </button>
              </div>
            </div>
          </div>
        </div>
        
        <div class="modal-footer">
          <button 
            type="button" 
            class="btn btn-secondary" 
            data-bs-dismiss="modal"
          >
            取消
          </button>
          <button 
            type="button" 
            class="btn btn-primary" 
            :disabled="selectedProblems.length === 0 || submitting"
            @click="handleAddProblems"
          >
            <span v-if="submitting" class="spinner-border spinner-border-sm me-2"></span>
            <i v-else class="bi bi-check-circle me-2"></i>
            {{ submitting ? '添加中...' : '确认添加' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useStore } from 'vuex'
import { showSuccess, showError } from '@/utils/message'
import type { Problem } from '@/types'

// 定义组件名称
defineOptions({
  name: 'AddProblemModal'
})

// 定义 Props 接口
interface Props {
  problemSetId: string | number
  existingProblemIds?: number[]
}

// 定义 Emits 接口
interface Emits {
  (e: 'added', data: { problemSetId: string | number; problemIds: number[] }): void
}

// 使用 defineProps 和 defineEmits
const props = withDefaults(defineProps<Props>(), {
  existingProblemIds: () => []
})

const emit = defineEmits<Emits>()
    const store = useStore()
    
    const loading = ref(false)
    const submitting = ref(false)
    const searchKeyword = ref('')
    const selectedDifficulty = ref('')
    const selectedProblems = ref([])
    const allProblems = ref([])

    const availableProblems = computed(() => {
      let filtered = allProblems.value.filter(problem =>
        !props.existingProblemIds.includes(problem.id)
      )

      // 关键词搜索（支持标题、描述、标签）
      if (searchKeyword.value) {
        const keyword = searchKeyword.value.toLowerCase()
        filtered = filtered.filter(problem =>
          problem.title.toLowerCase().includes(keyword) ||
          problem.description.toLowerCase().includes(keyword) ||
          (problem.tags && problem.tags.toLowerCase().includes(keyword))
        )
      }

      // 难度筛选
      if (selectedDifficulty.value) {
        filtered = filtered.filter(problem => problem.difficulty === selectedDifficulty.value)
      }

      return filtered
    })

    // 搜索建议
    const searchSuggestions = computed(() => {
      if (!searchKeyword.value || searchKeyword.value.length < 2) return []

      const suggestions = new Set()
      const keyword = searchKeyword.value.toLowerCase()

      allProblems.value.forEach(problem => {
        // 从标签中提取建议
        if (problem.tags) {
          problem.tags.split(',').forEach(tag => {
            const trimmedTag = tag.trim()
            if (trimmedTag.toLowerCase().includes(keyword) && trimmedTag.length > 1) {
              suggestions.add(trimmedTag)
            }
          })
        }

        // 从标题中提取建议
        if (problem.title.toLowerCase().includes(keyword)) {
          const words = problem.title.split(/\s+/)
          words.forEach(word => {
            if (word.toLowerCase().includes(keyword) && word.length > 2) {
              suggestions.add(word)
            }
          })
        }
      })

      return Array.from(suggestions).slice(0, 8)
    })

    const getDifficultyClass = (difficulty) => {
      const classes = {
        'EASY': 'bg-success',
        'MEDIUM': 'bg-warning',
        'HARD': 'bg-danger'
      }
      return classes[difficulty] || 'bg-secondary'
    }

    const getDifficultyText = (difficulty) => {
      const texts = {
        'EASY': '简单',
        'MEDIUM': '中等',
        'HARD': '困难'
      }
      return texts[difficulty] || difficulty
    }

    const getTagList = (tags) => {
      if (!tags) return []
      return tags.split(',').map(tag => tag.trim()).filter(tag => tag)
    }

    const loadAvailableProblems = async () => {
      loading.value = true
      try {
        await store.dispatch('problem/fetchProblems', {
          current: 1,
          size: 100 // 获取更多题目
        })
        
        allProblems.value = store.getters['problem/problems']
      } catch (error) {
        showError('获取题目列表失败')
        console.error('获取题目列表失败:', error)
      } finally {
        loading.value = false
      }
    }

    const handleSearch = () => {
      // 搜索逻辑已在 computed 中处理
      // 重置选择状态
      selectedProblems.value = []
    }

    const clearSearch = () => {
      searchKeyword.value = ''
      selectedProblems.value = []
    }

    const applySuggestion = (suggestion) => {
      searchKeyword.value = suggestion
    }

    const highlightSearchTerm = (text) => {
      if (!searchKeyword.value || !text) return text

      const keyword = searchKeyword.value.trim()
      if (!keyword) return text

      const regex = new RegExp(`(${keyword})`, 'gi')
      return text.replace(regex, '<mark class="search-highlight">$1</mark>')
    }

    const selectAll = () => {
      selectedProblems.value = availableProblems.value.map(p => p.id)
    }

    const clearSelection = () => {
      selectedProblems.value = []
    }

    const handleQuickAdd = () => {
      if (availableProblems.value.length > 0) {
        quickAddSingle(availableProblems.value[0])
      }
    }

    const quickAddSingle = async (problem) => {
      try {
        await addProblemToProblemSet(problem.id, props.problemSetId)
        showSuccess(`题目"${problem.title}"已添加到题集`)

        // 触发事件
        emit('added', {
          problemSetId: props.problemSetId,
          problemIds: [problem.id]
        })

        // 从可用列表中移除
        allProblems.value = allProblems.value.filter(p => p.id !== problem.id)
      } catch (error) {
        showError('添加失败：' + error.message)
      }
    }

    const handleAddProblems = async () => {
      if (selectedProblems.value.length === 0) {
        showError('请选择至少一个题目')
        return
      }

      submitting.value = true
      try {
        // 模拟添加题目到题集的API调用
        for (const problemId of selectedProblems.value) {
          await addProblemToProblemSet(problemId, props.problemSetId)
        }

        showSuccess(`已成功添加 ${selectedProblems.value.length} 个题目到题集`)
        
        // 关闭模态框
        const modal = document.getElementById('addProblemModal')
        const bsModal = window.bootstrap.Modal.getInstance(modal)
        if (bsModal) {
          bsModal.hide()
        }

        // 触发事件
        emit('added', {
          problemSetId: props.problemSetId,
          problemIds: selectedProblems.value
        })

        // 重置选择
        selectedProblems.value = []
        searchKeyword.value = ''
        selectedDifficulty.value = ''
      } catch (error) {
        showError('添加失败：' + error.message)
      } finally {
        submitting.value = false
      }
    }

    const addProblemToProblemSet = async (problemId, problemSetId) => {
      try {
        await store.dispatch('problemset/addProblemToProblemSet', {
          problemSetId,
          problemId
        })
      } catch (error) {
        throw new Error(error.message || '添加失败')
      }
    }

    // 监听难度变化，重置选择
    watch(selectedDifficulty, () => {
      selectedProblems.value = []
    })

    // 监听搜索关键词变化，重置选择
    watch(searchKeyword, () => {
      selectedProblems.value = []
    })

    onMounted(() => {
      loadAvailableProblems()
    })

    return {
      loading,
      submitting,
      searchKeyword,
      selectedDifficulty,
      selectedProblems,
      availableProblems,
      searchSuggestions,
      getDifficultyClass,
      getDifficultyText,
      getTagList,
      handleSearch,
      clearSearch,
      applySuggestion,
      highlightSearchTerm,
      selectAll,
      clearSelection,
      handleQuickAdd,
      quickAddSingle,
      handleAddProblems
    }
  }
}
</script>

<style scoped>
.problem-item {
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
  transition: all 0.2s ease-in-out;
  background: white;
}

.problem-item:hover {
  border-color: #007bff;
  background-color: #f8f9fa;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
}

.problem-item.selected {
  border-color: #007bff;
  background-color: #e7f3ff;
}

.form-check {
  padding: 1rem;
  margin-bottom: 0;
}

.form-check-input:checked + .form-check-label {
  color: #007bff;
}

.form-check-label {
  cursor: pointer;
  width: 100%;
}

.problem-list {
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
  padding: 0.5rem;
  background-color: #fafafa;
}

.problem-index {
  font-size: 0.75rem;
  color: #6c757d;
  font-weight: 500;
  min-width: 30px;
}

.problem-title {
  color: #212529;
  line-height: 1.4;
}

.problem-description {
  line-height: 1.4;
  max-height: 2.8em;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.problem-meta {
  flex-wrap: wrap;
}

.problem-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
}

.problem-actions {
  margin-left: 0.5rem;
}

.badge {
  font-size: 0.75em;
}

.search-highlight {
  background-color: #fff3cd;
  color: #856404;
  padding: 0.1em 0.2em;
  border-radius: 0.2em;
  font-weight: 500;
}

.search-suggestions {
  padding: 0.75rem;
  background-color: #f8f9fa;
  border-radius: 0.375rem;
  border: 1px solid #e9ecef;
}

.suggestion-tag {
  cursor: pointer;
  transition: all 0.2s ease;
}

.suggestion-tag:hover {
  background-color: #007bff !important;
  color: white !important;
  transform: scale(1.05);
}

.search-stats {
  padding: 0.5rem 0.75rem;
  background-color: #f8f9fa;
  border-radius: 0.375rem;
  border: 1px solid #e9ecef;
}

.selected-summary {
  position: sticky;
  bottom: 0;
  background: white;
  border-top: 1px solid #dee2e6;
  margin: 0 -1rem -1rem -1rem;
  padding: 1rem;
}

.btn-check:checked + .btn {
  background-color: var(--bs-btn-active-bg);
  border-color: var(--bs-btn-active-border-color);
  color: var(--bs-btn-active-color);
}

/* 滚动条样式 */
.problem-list::-webkit-scrollbar {
  width: 6px;
}

.problem-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.problem-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.problem-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .problem-item .form-check {
    padding: 0.75rem;
  }

  .problem-description {
    -webkit-line-clamp: 1;
    max-height: 1.4em;
  }

  .problem-actions {
    margin-left: 0;
    margin-top: 0.5rem;
  }

  .d-flex.justify-content-between {
    flex-direction: column;
    align-items: flex-start !important;
  }
}
</style>
