<template>
  <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
    <div class="container">
      <!-- 品牌Logo -->
      <router-link class="navbar-brand fw-bold" to="/">
        <i class="bi bi-code-square me-2"></i>
        Code-Combined
      </router-link>

      <!-- 移动端切换按钮 -->
      <button 
        class="navbar-toggler" 
        type="button" 
        data-bs-toggle="collapse" 
        data-bs-target="#navbarNav"
        aria-controls="navbarNav" 
        aria-expanded="false" 
        aria-label="Toggle navigation"
      >
        <span class="navbar-toggler-icon"></span>
      </button>

      <!-- 导航菜单 -->
      <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav me-auto">
          <li class="nav-item">
            <router-link class="nav-link" to="/">
              <i class="bi bi-house me-1"></i>首页
            </router-link>
          </li>
          <li class="nav-item">
            <router-link class="nav-link" to="/problemsets">
              <i class="bi bi-collection me-1"></i>题集
            </router-link>
          </li>
          <li class="nav-item">
            <router-link class="nav-link" to="/problems">
              <i class="bi bi-puzzle me-1"></i>题目
            </router-link>
          </li>
        </ul>

        <!-- 用户状态统计 -->
        <div class="navbar-text me-3 d-none d-lg-block">
          <small class="text-light">
            <i class="bi bi-people me-1"></i>
            在线: {{ onlineUsers }} | 总计: {{ totalUsers }}
          </small>
        </div>

        <!-- 用户菜单 -->
        <ul class="navbar-nav">
          <template v-if="isAuthenticated">
            <!-- 创建菜单 -->
            <li class="nav-item dropdown">
              <a 
                class="nav-link dropdown-toggle" 
                href="#" 
                role="button" 
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                <i class="bi bi-plus-circle me-1"></i>创建
              </a>
              <ul class="dropdown-menu">
                <li>
                  <router-link class="dropdown-item" to="/problemsets/create">
                    <i class="bi bi-collection me-2"></i>新建题集
                  </router-link>
                </li>
                <li>
                  <router-link class="dropdown-item" to="/problems/create">
                    <i class="bi bi-puzzle me-2"></i>新建题目
                  </router-link>
                </li>
              </ul>
            </li>

            <!-- 用户菜单 -->
            <li class="nav-item dropdown">
              <a 
                class="nav-link dropdown-toggle" 
                href="#" 
                role="button" 
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                <i class="bi bi-person-circle me-1"></i>
                {{ user?.nickname || user?.username }}
              </a>
              <ul class="dropdown-menu">
                <li>
                  <router-link class="dropdown-item" to="/dashboard">
                    <i class="bi bi-speedometer2 me-2"></i>控制台
                  </router-link>
                </li>
                <li>
                  <router-link class="dropdown-item" to="/profile">
                    <i class="bi bi-person me-2"></i>个人资料
                  </router-link>
                </li>
                <li v-if="user?.role === 'admin'">
                  <router-link class="dropdown-item" to="/admin">
                    <i class="bi bi-shield-check me-2"></i>管理控制台
                  </router-link>
                </li>
                <li><hr class="dropdown-divider"></li>
                <li>
                  <a class="dropdown-item" href="#" @click="handleLogout">
                    <i class="bi bi-box-arrow-right me-2"></i>退出登录
                  </a>
                </li>
              </ul>
            </li>
          </template>

          <template v-else>
            <li class="nav-item">
              <router-link class="nav-link" to="/login">
                <i class="bi bi-box-arrow-in-right me-1"></i>登录
              </router-link>
            </li>
            <li class="nav-item">
              <router-link class="btn btn-outline-light btn-sm ms-2" to="/register">
                注册
              </router-link>
            </li>
          </template>
        </ul>
      </div>
    </div>
  </nav>
</template>

<script>
import { computed, onMounted, onUnmounted } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'

export default {
  name: 'Navbar',
  setup() {
    const store = useStore()
    const router = useRouter()

    const isAuthenticated = computed(() => store.getters['auth/isAuthenticated'])
    const user = computed(() => store.getters['auth/user'])
    const onlineUsers = computed(() => store.state.stats.onlineUsers)
    const totalUsers = computed(() => store.state.stats.totalUsers)

    const handleLogout = () => {
      store.dispatch('auth/logout')
      router.push('/')
    }

    // 获取统计数据
    const fetchStats = () => {
      store.dispatch('stats/fetchUserStats')
    }

    onMounted(() => {
      fetchStats()
      // 每30秒更新一次统计数据
      const interval = setInterval(fetchStats, 30000)
      
      onUnmounted(() => {
        clearInterval(interval)
      })
    })

    return {
      isAuthenticated,
      user,
      onlineUsers,
      totalUsers,
      handleLogout
    }
  }
}
</script>

<style scoped>
.navbar-brand {
  font-size: 1.5rem;
}

.nav-link {
  transition: color 0.15s ease-in-out;
}

.nav-link:hover {
  color: rgba(255, 255, 255, 0.8) !important;
}

.router-link-active {
  color: rgba(255, 255, 255, 0.9) !important;
  font-weight: 500;
}

.dropdown-menu {
  border: none;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.dropdown-item {
  transition: background-color 0.15s ease-in-out;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
}

@media (max-width: 991px) {
  .navbar-nav .nav-link {
    padding: 0.5rem 1rem;
  }
}
</style>
