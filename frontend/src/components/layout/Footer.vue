<template>
  <footer class="bg-dark text-light py-4 mt-5">
    <div class="container">
      <div class="row">
        <div class="col-md-6">
          <h5 class="mb-3">
            <i class="bi bi-code-square me-2"></i>
            Code-Combined
          </h5>
          <p class="text-muted">
            专业的算法题集管理平台，帮助你更好地组织和学习算法题目。
          </p>
          <div class="d-flex gap-3">
            <a href="#" class="text-light">
              <i class="bi bi-github fs-5"></i>
            </a>
            <a href="#" class="text-light">
              <i class="bi bi-envelope fs-5"></i>
            </a>
            <a href="#" class="text-light">
              <i class="bi bi-twitter fs-5"></i>
            </a>
          </div>
        </div>
        
        <div class="col-md-3">
          <h6 class="mb-3">快速链接</h6>
          <ul class="list-unstyled">
            <li class="mb-2">
              <router-link to="/problemsets" class="text-muted text-decoration-none">
                题集列表
              </router-link>
            </li>
            <li class="mb-2">
              <router-link to="/problems" class="text-muted text-decoration-none">
                题目列表
              </router-link>
            </li>
            <li class="mb-2">
              <a href="#" class="text-muted text-decoration-none">
                使用帮助
              </a>
            </li>
            <li class="mb-2">
              <a href="#" class="text-muted text-decoration-none">
                API文档
              </a>
            </li>
          </ul>
        </div>
        
        <div class="col-md-3">
          <h6 class="mb-3">关于我们</h6>
          <ul class="list-unstyled">
            <li class="mb-2">
              <a href="#" class="text-muted text-decoration-none">
                团队介绍
              </a>
            </li>
            <li class="mb-2">
              <a href="#" class="text-muted text-decoration-none">
                联系我们
              </a>
            </li>
            <li class="mb-2">
              <a href="#" class="text-muted text-decoration-none">
                隐私政策
              </a>
            </li>
            <li class="mb-2">
              <a href="#" class="text-muted text-decoration-none">
                服务条款
              </a>
            </li>
          </ul>
        </div>
      </div>
      
      <hr class="my-4">
      
      <div class="row align-items-center">
        <div class="col-md-6">
          <p class="text-muted mb-0">
            &copy; 2025 Code-Combined. All rights reserved.
          </p>
        </div>
        <div class="col-md-6 text-md-end">
          <p class="text-muted mb-0">
            Built with Vue 3 & Spring Boot
          </p>
        </div>
      </div>
    </div>
  </footer>
</template>

<script>
export default {
  name: 'Footer'
}
</script>

<style scoped>
footer a:hover {
  color: #fff !important;
  transition: color 0.15s ease-in-out;
}
</style>
