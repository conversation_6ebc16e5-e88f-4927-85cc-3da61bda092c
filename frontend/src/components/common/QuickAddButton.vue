<template>
  <!-- 快速添加悬浮按钮 -->
  <div class="quick-add-container">
    <!-- 主按钮 -->
    <button 
      v-if="isAuthenticated"
      @click="toggleMenu" 
      class="quick-add-btn"
      :class="{ 'active': showMenu }"
      title="快速添加"
    >
      <i class="bi bi-plus" :class="{ 'rotate': showMenu }"></i>
    </button>

    <!-- 菜单选项 -->
    <transition name="menu">
      <div v-if="showMenu" class="quick-add-menu">
        <button 
          @click="createProblem"
          class="menu-item"
          title="创建题目"
        >
          <i class="bi bi-puzzle"></i>
          <span>题目</span>
        </button>
        
        <button 
          @click="createProblemSet"
          class="menu-item"
          title="创建题集"
        >
          <i class="bi bi-collection"></i>
          <span>题集</span>
        </button>

        <button 
          v-if="userProblemSets.length > 0"
          @click="showQuickAddModal"
          class="menu-item"
          title="添加题目到题集"
        >
          <i class="bi bi-plus-circle"></i>
          <span>添加</span>
        </button>
      </div>
    </transition>

    <!-- 遮罩层 -->
    <div 
      v-if="showMenu" 
      class="quick-add-overlay"
      @click="closeMenu"
    ></div>
  </div>

  <!-- 快速添加题目模态框 -->
  <div 
    class="modal fade" 
    id="quickAddModal" 
    tabindex="-1" 
    aria-labelledby="quickAddModalLabel" 
    aria-hidden="true"
  >
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="quickAddModalLabel">
            <i class="bi bi-lightning me-2"></i>
            快速添加题目
          </h5>
          <button 
            type="button" 
            class="btn-close" 
            data-bs-dismiss="modal" 
            aria-label="Close"
          ></button>
        </div>
        
        <div class="modal-body">
          <div class="mb-3">
            <label class="form-label">选择题集：</label>
            <select v-model="selectedQuickProblemSetId" class="form-select">
              <option value="">请选择题集</option>
              <option 
                v-for="problemSet in userProblemSets" 
                :key="problemSet.id"
                :value="problemSet.id"
              >
                {{ problemSet.name }} ({{ problemSet.problemCount }} 题目)
              </option>
            </select>
          </div>
          
          <div class="alert alert-info">
            <i class="bi bi-info-circle me-2"></i>
            选择题集后将打开添加题目界面
          </div>
        </div>
        
        <div class="modal-footer">
          <button 
            type="button" 
            class="btn btn-secondary" 
            data-bs-dismiss="modal"
          >
            取消
          </button>
          <button 
            type="button" 
            class="btn btn-primary" 
            :disabled="!selectedQuickProblemSetId"
            @click="openAddProblemModal"
          >
            <i class="bi bi-arrow-right me-2"></i>
            继续
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 添加题目模态框 -->
  <AddProblemModal 
    v-if="selectedQuickProblemSet"
    :problem-set-id="selectedQuickProblemSet.id"
    :existing-problem-ids="[]"
    @added="handleQuickAdded"
  />
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import { showSuccess } from '@/utils/message'
import AddProblemModal from '@/components/problemset/AddProblemModal.vue'
import type { ProblemSet, User } from '@/types'

// 定义组件名称
defineOptions({
  name: 'QuickAddButton'
})

const store = useStore()
const router = useRouter()

const showMenu = ref<boolean>(false)
const userProblemSets = ref<ProblemSet[]>([])
const selectedQuickProblemSetId = ref<string>('')
const selectedQuickProblemSet = ref<ProblemSet | null>(null)

const isAuthenticated = computed((): boolean => store.getters['auth/isAuthenticated'])
const user = computed((): User | null => store.getters['auth/user'])

    const toggleMenu = () => {
      showMenu.value = !showMenu.value
    }

    const closeMenu = () => {
      showMenu.value = false
    }

    const createProblem = () => {
      closeMenu()
      router.push('/problems/create')
    }

    const createProblemSet = () => {
      closeMenu()
      router.push('/problemsets/create')
    }

    const showQuickAddModal = () => {
      closeMenu()
      const modal = new window.bootstrap.Modal(document.getElementById('quickAddModal'))
      modal.show()
    }

    const openAddProblemModal = () => {
      const problemSet = userProblemSets.value.find(ps => ps.id == selectedQuickProblemSetId.value)
      if (problemSet) {
        selectedQuickProblemSet.value = problemSet
        
        // 关闭选择模态框
        const quickModal = window.bootstrap.Modal.getInstance(document.getElementById('quickAddModal'))
        if (quickModal) {
          quickModal.hide()
        }
        
        // 打开添加题目模态框
        setTimeout(() => {
          const addModal = new window.bootstrap.Modal(document.getElementById('addProblemModal'))
          addModal.show()
        }, 300)
      }
    }

    const handleQuickAdded = (data) => {
      showSuccess(`已成功添加 ${data.problemIds.length} 个题目`)
      selectedQuickProblemSet.value = null
      selectedQuickProblemSetId.value = ''
    }

    const loadUserProblemSets = async () => {
      if (!user.value) return
      
      try {
        await store.dispatch('problemset/fetchProblemSets', {
          current: 1,
          size: 50
        })
        
        // 过滤出用户可编辑的题集
        userProblemSets.value = store.getters['problemset/problemSets'].filter(ps => 
          user.value.role === 'ADMIN' || ps.creatorId === user.value.id
        )
      } catch (error) {
        console.error('获取用户题集失败:', error)
      }
    }

    // 点击外部关闭菜单
    const handleClickOutside = (event) => {
      const container = event.target.closest('.quick-add-container')
      if (!container && showMenu.value) {
        closeMenu()
      }
    }

    onMounted(() => {
      document.addEventListener('click', handleClickOutside)
      if (isAuthenticated.value) {
        loadUserProblemSets()
      }
    })

    onUnmounted(() => {
      document.removeEventListener('click', handleClickOutside)
    })

    return {
      showMenu,
      isAuthenticated,
      userProblemSets,
      selectedQuickProblemSetId,
      selectedQuickProblemSet,
      toggleMenu,
      closeMenu,
      createProblem,
      createProblemSet,
      showQuickAddModal,
      openAddProblemModal,
      handleQuickAdded
    }
  }
}
</script>

<style scoped>
.quick-add-container {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 1000;
}

.quick-add-btn {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: linear-gradient(135deg, #007bff, #0056b3);
  border: none;
  color: white;
  font-size: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 1002;
}

.quick-add-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
}

.quick-add-btn.active {
  background: linear-gradient(135deg, #dc3545, #c82333);
}

.quick-add-btn i.rotate {
  transform: rotate(45deg);
}

.quick-add-menu {
  position: absolute;
  bottom: 70px;
  right: 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  z-index: 1001;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 2rem;
  color: #495057;
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  white-space: nowrap;
  min-width: 120px;
}

.menu-item:hover {
  background: #007bff;
  color: white;
  transform: translateX(-5px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.menu-item i {
  font-size: 1rem;
}

.quick-add-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  z-index: 999;
}

/* 动画效果 */
.menu-enter-active,
.menu-leave-active {
  transition: all 0.3s ease;
}

.menu-enter-from {
  opacity: 0;
  transform: translateY(20px) scale(0.8);
}

.menu-leave-to {
  opacity: 0;
  transform: translateY(20px) scale(0.8);
}

.menu-item {
  animation: slideIn 0.3s ease forwards;
}

.menu-item:nth-child(1) { animation-delay: 0.1s; }
.menu-item:nth-child(2) { animation-delay: 0.2s; }
.menu-item:nth-child(3) { animation-delay: 0.3s; }

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .quick-add-container {
    bottom: 1rem;
    right: 1rem;
  }
  
  .quick-add-btn {
    width: 48px;
    height: 48px;
    font-size: 1.25rem;
  }
  
  .menu-item {
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
    min-width: 100px;
  }
}
</style>
