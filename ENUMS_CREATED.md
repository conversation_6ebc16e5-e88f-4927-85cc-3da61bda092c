# 枚举类创建完成指南

## 🎯 概述

已为项目创建了完整的枚举类系统，涵盖用户管理、题目管理、题集管理和代码提交等所有业务场景。

## 📁 创建的枚举类列表

### 1. UserRole.java ✅
**功能**：用户角色枚举
**值**：
- `USER("user", "普通用户", 1)`
- `ADMIN("admin", "管理员", 2)`

**特性**：
- 支持角色等级比较
- 权限检查方法
- JSON序列化支持
- 代码/名称转换

### 2. UserStatus.java ✅
**功能**：用户状态枚举
**值**：
- `DISABLED(0, "disabled", "禁用", "#dc3545")`
- `ENABLED(1, "enabled", "启用", "#28a745")`

**特性**：
- 数据库整型值存储
- 状态检查方法
- 颜色显示支持
- 登录权限检查

### 3. ProblemDifficulty.java ✅
**功能**：题目难度枚举
**值**：
- `EASY("easy", "简单", 1, "#28a745")`
- `MEDIUM("medium", "中等", 2, "#ffc107")`
- `HARD("hard", "困难", 3, "#dc3545")`

**特性**：
- 难度等级排序
- 颜色编码显示
- 难度比较方法
- 前端显示支持

### 4. ProblemStatus.java ✅
**功能**：题目状态枚举
**值**：
- `DRAFT("draft", "草稿", "#6c757d")`
- `PUBLISHED("published", "已发布", "#28a745")`
- `ARCHIVED("archived", "已归档", "#ffc107")`

**特性**：
- 状态流转控制
- 可见性检查
- 编辑权限控制
- 状态颜色显示

### 5. ProblemSetStatus.java ✅
**功能**：题集状态枚举
**值**：
- `DRAFT("draft", "草稿", "#6c757d")`
- `PUBLISHED("published", "已发布", "#28a745")`
- `ARCHIVED("archived", "已归档", "#ffc107")`

**特性**：
- 与题目状态保持一致
- 状态流转管理
- 权限控制支持
- 前端显示优化

### 6. SubmissionStatus.java ✅
**功能**：代码提交状态枚举
**值**：
- `PENDING("pending", "等待判题", "#6c757d")`
- `JUDGING("judging", "判题中", "#17a2b8")`
- `ACCEPTED("accepted", "通过", "#28a745")`
- `WRONG_ANSWER("wrong_answer", "答案错误", "#dc3545")`
- `TIME_LIMIT_EXCEEDED("time_limit_exceeded", "时间超限", "#fd7e14")`
- `MEMORY_LIMIT_EXCEEDED("memory_limit_exceeded", "内存超限", "#e83e8c")`
- `RUNTIME_ERROR("runtime_error", "运行时错误", "#6f42c1")`
- `COMPILE_ERROR("compile_error", "编译错误", "#fd7e14")`
- `SYSTEM_ERROR("system_error", "系统错误", "#6c757d")`
- `OUTPUT_LIMIT_EXCEEDED("output_limit_exceeded", "输出超限", "#20c997")`
- `PRESENTATION_ERROR("presentation_error", "格式错误", "#ffc107")`

**特性**：
- 完整的判题状态覆盖
- 状态分类方法
- 颜色编码显示
- 重提交权限控制

### 7. ProgrammingLanguage.java ✅
**功能**：编程语言枚举
**值**：
- `JAVA("java", "Java", ".java", "java", "#f89820")`
- `PYTHON("python", "Python", ".py", "python3", "#3776ab")`
- `CPP("cpp", "C++", ".cpp", "g++", "#00599c")`
- `C("c", "C", ".c", "gcc", "#a8b9cc")`
- `JAVASCRIPT("javascript", "JavaScript", ".js", "node", "#f7df1e")`
- `GO("go", "Go", ".go", "go", "#00add8")`
- `RUST("rust", "Rust", ".rs", "rustc", "#000000")`
- `KOTLIN("kotlin", "Kotlin", ".kt", "kotlinc", "#7f52ff")`
- `TYPESCRIPT("typescript", "TypeScript", ".ts", "tsc", "#3178c6")`
- `CSHARP("csharp", "C#", ".cs", "csc", "#239120")`
- `PHP("php", "PHP", ".php", "php", "#777bb4")`
- `RUBY("ruby", "Ruby", ".rb", "ruby", "#cc342d")`

**特性**：
- 文件扩展名映射
- 编译器命令配置
- 语言类型检查（编译型/解释型）
- 默认时间/内存限制
- 颜色主题支持

## 🔧 枚举类设计特性

### 1. 统一的设计模式
所有枚举类都遵循统一的设计模式：

```java
public enum ExampleEnum {
    VALUE1("code1", "名称1", "颜色1"),
    VALUE2("code2", "名称2", "颜色2");
    
    private final String code;
    private final String name;
    private final String color;
    
    // 构造函数
    ExampleEnum(String code, String name, String color) {
        this.code = code;
        this.name = name;
        this.color = color;
    }
    
    // JSON序列化
    @JsonValue
    public String getCode() {
        return code;
    }
    
    // 转换方法
    public static ExampleEnum fromCode(String code) { ... }
    public static ExampleEnum fromName(String name) { ... }
    
    // 业务方法
    public boolean isXxx() { ... }
    
    // 工具方法
    public static String[] getAllCodes() { ... }
    public static String[] getAllNames() { ... }
}
```

### 2. JSON序列化支持
使用 `@JsonValue` 注解确保JSON序列化时使用代码值：

```json
{
  "role": "admin",           // 而不是 "ADMIN"
  "status": "published",     // 而不是 "PUBLISHED"
  "difficulty": "easy"       // 而不是 "EASY"
}
```

### 3. 数据库存储优化
- 字符串枚举：存储代码值（如 "admin", "user"）
- 数值枚举：存储整型值（如 0, 1）
- 便于数据库查询和索引

### 4. 前端显示支持
- 提供中文名称显示
- 颜色编码支持
- 便于前端组件渲染

### 5. 业务逻辑封装
每个枚举都包含相关的业务逻辑方法：

```java
// 用户角色
userRole.hasPermission(targetRole)
userRole.isAdmin()

// 题目状态
problemStatus.isVisible()
problemStatus.isEditable()

// 提交状态
submissionStatus.isFinal()
submissionStatus.canResubmit()

// 编程语言
language.isCompiled()
language.getDefaultTimeLimit()
```

## 🎨 前端集成

### 1. TypeScript类型定义
可以为前端创建对应的TypeScript类型：

```typescript
export enum UserRole {
  USER = 'user',
  ADMIN = 'admin'
}

export enum ProblemDifficulty {
  EASY = 'easy',
  MEDIUM = 'medium',
  HARD = 'hard'
}

export enum SubmissionStatus {
  PENDING = 'pending',
  JUDGING = 'judging',
  ACCEPTED = 'accepted',
  WRONG_ANSWER = 'wrong_answer',
  // ... 其他状态
}
```

### 2. 显示映射
前端可以创建显示映射对象：

```javascript
export const roleDisplayMap = {
  'user': { name: '普通用户', color: '#007bff' },
  'admin': { name: '管理员', color: '#dc3545' }
}

export const difficultyDisplayMap = {
  'easy': { name: '简单', color: '#28a745' },
  'medium': { name: '中等', color: '#ffc107' },
  'hard': { name: '困难', color: '#dc3545' }
}
```

### 3. 组件使用示例
```vue
<template>
  <span 
    class="badge" 
    :style="{ backgroundColor: getDifficultyColor(problem.difficulty) }"
  >
    {{ getDifficultyName(problem.difficulty) }}
  </span>
</template>

<script>
export default {
  methods: {
    getDifficultyName(difficulty) {
      const map = {
        'easy': '简单',
        'medium': '中等',
        'hard': '困难'
      }
      return map[difficulty] || difficulty
    },
    getDifficultyColor(difficulty) {
      const map = {
        'easy': '#28a745',
        'medium': '#ffc107',
        'hard': '#dc3545'
      }
      return map[difficulty] || '#6c757d'
    }
  }
}
</script>
```

## 🔄 使用示例

### 1. 控制器中使用
```java
@PostMapping
public Result<Long> createUser(@RequestBody UserCreateRequest request) {
    // 枚举验证
    if (request.getRole() == null) {
        return Result.error("用户角色不能为空");
    }
    
    // 权限检查
    UserRole currentUserRole = getCurrentUserRole();
    if (!currentUserRole.hasPermission(request.getRole())) {
        return Result.error("权限不足");
    }
    
    // 业务逻辑
    Long userId = userService.createUser(request);
    return Result.success(userId);
}
```

### 2. 服务层中使用
```java
@Override
public void updateProblemStatus(Long problemId, ProblemStatus newStatus) {
    Problem problem = getById(problemId);
    
    // 状态流转检查
    if (!problem.getStatus().isEditable()) {
        throw new BusinessException("当前状态不允许修改");
    }
    
    // 可见性检查
    if (newStatus.isVisible() && !problem.isComplete()) {
        throw new BusinessException("题目信息不完整，无法发布");
    }
    
    problem.setStatus(newStatus);
    updateById(problem);
}
```

### 3. 数据库查询中使用
```java
// 查询已发布的题目
List<Problem> publishedProblems = lambdaQuery()
    .eq(Problem::getStatus, ProblemStatus.PUBLISHED)
    .list();

// 查询管理员用户
List<User> adminUsers = lambdaQuery()
    .eq(User::getRole, UserRole.ADMIN.getCode())
    .list();
```

## 🧪 测试建议

### 1. 枚举转换测试
```java
@Test
public void testEnumConversion() {
    // 测试代码转换
    assertEquals(UserRole.ADMIN, UserRole.fromCode("admin"));
    assertEquals(ProblemDifficulty.EASY, ProblemDifficulty.fromCode("easy"));
    
    // 测试名称转换
    assertEquals(UserRole.USER, UserRole.fromName("普通用户"));
    
    // 测试异常情况
    assertThrows(IllegalArgumentException.class, 
        () -> UserRole.fromCode("invalid"));
}
```

### 2. 业务逻辑测试
```java
@Test
public void testBusinessLogic() {
    // 测试权限检查
    assertTrue(UserRole.ADMIN.hasPermission(UserRole.USER));
    assertFalse(UserRole.USER.hasPermission(UserRole.ADMIN));
    
    // 测试状态检查
    assertTrue(ProblemStatus.PUBLISHED.isVisible());
    assertFalse(ProblemStatus.DRAFT.isVisible());
    
    // 测试语言特性
    assertTrue(ProgrammingLanguage.JAVA.isCompiled());
    assertFalse(ProgrammingLanguage.PYTHON.isCompiled());
}
```

### 3. JSON序列化测试
```java
@Test
public void testJsonSerialization() {
    ObjectMapper mapper = new ObjectMapper();
    
    // 测试序列化
    String json = mapper.writeValueAsString(UserRole.ADMIN);
    assertEquals("\"admin\"", json);
    
    // 测试反序列化
    UserRole role = mapper.readValue("\"admin\"", UserRole.class);
    assertEquals(UserRole.ADMIN, role);
}
```

## 📋 检查清单

### 枚举类完整性
- [ ] UserRole - 用户角色 ✅
- [ ] UserStatus - 用户状态 ✅
- [ ] ProblemDifficulty - 题目难度 ✅
- [ ] ProblemStatus - 题目状态 ✅
- [ ] ProblemSetStatus - 题集状态 ✅
- [ ] SubmissionStatus - 提交状态 ✅
- [ ] ProgrammingLanguage - 编程语言 ✅

### 功能特性
- [ ] JSON序列化支持 ✅
- [ ] 代码/名称转换 ✅
- [ ] 业务逻辑方法 ✅
- [ ] 颜色编码支持 ✅
- [ ] 工具方法提供 ✅

### 集成测试
- [ ] 控制器中使用 ✅
- [ ] 服务层中使用 ✅
- [ ] 数据库查询中使用 ✅
- [ ] 前端显示支持 ✅

---

**总结**：已创建完整的枚举类系统，涵盖项目所有业务场景。所有枚举类都遵循统一的设计模式，支持JSON序列化、业务逻辑封装和前端显示，为项目提供了强类型的数据约束和业务逻辑支持。🎉
