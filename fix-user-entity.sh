#!/bin/bash

# User实体类字段修复脚本
# 解决 getPoints() 方法找不到的编译错误

echo "🔧 开始修复User实体类字段问题..."

# 进入后端项目目录
cd code-combined-backend

echo "📋 检查当前项目状态..."

# 检查Lombok依赖
echo "🔍 检查Lombok依赖..."
if grep -q "lombok" pom.xml; then
    echo "✅ Lombok依赖已配置"
else
    echo "❌ Lombok依赖缺失"
    exit 1
fi

# 检查User实体类
echo "🔍 检查User实体类..."
if [ -f "src/main/java/com/codecombined/entity/User.java" ]; then
    echo "✅ User实体类存在"
    
    # 检查是否有@Data注解
    if grep -q "@Data" src/main/java/com/codecombined/entity/User.java; then
        echo "✅ @Data注解已配置"
    else
        echo "❌ @Data注解缺失"
        exit 1
    fi
    
    # 检查是否有points字段
    if grep -q "private Integer points" src/main/java/com/codecombined/entity/User.java; then
        echo "✅ points字段已添加"
    else
        echo "❌ points字段缺失"
        exit 1
    fi
else
    echo "❌ User实体类不存在"
    exit 1
fi

echo "🧹 清理项目..."
mvn clean -q

echo "🔨 重新编译项目..."
if mvn compile -q; then
    echo "✅ 编译成功"
else
    echo "❌ 编译失败，请检查以下可能的问题："
    echo "   1. IDE是否安装了Lombok插件"
    echo "   2. IDE是否启用了注解处理"
    echo "   3. 项目是否正确导入"
    echo ""
    echo "🔧 IDE配置指南："
    echo "   IntelliJ IDEA:"
    echo "   - File → Settings → Plugins → 搜索'Lombok'并安装"
    echo "   - File → Settings → Build → Compiler → Annotation Processors → 勾选'Enable annotation processing'"
    echo ""
    echo "   Eclipse:"
    echo "   - Help → Eclipse Marketplace → 搜索'Lombok'并安装"
    echo "   - 重启Eclipse"
    echo ""
    echo "🔄 手动解决步骤："
    echo "   1. 重新导入项目"
    echo "   2. 清理并重新编译"
    echo "   3. 重启IDE"
    
    exit 1
fi

echo "🧪 运行测试验证..."
if mvn test -Dtest=*User* -q 2>/dev/null; then
    echo "✅ 用户相关测试通过"
else
    echo "⚠️  测试可能有问题，但编译已成功"
fi

echo "📊 检查数据库迁移脚本..."
if [ -f "src/main/resources/db/migration/V2__Add_User_Extended_Fields.sql" ]; then
    echo "✅ 数据库迁移脚本已创建"
    echo "📝 请确保运行以下SQL来更新数据库表结构："
    echo ""
    cat src/main/resources/db/migration/V2__Add_User_Extended_Fields.sql
    echo ""
else
    echo "❌ 数据库迁移脚本缺失"
fi

echo ""
echo "🎉 User实体类字段修复完成！"
echo ""
echo "📋 验证清单："
echo "   ✅ User.java中已添加points等新字段"
echo "   ✅ @Data注解配置正确"
echo "   ✅ Lombok依赖已配置"
echo "   ✅ 项目编译成功"
echo ""
echo "🔄 下一步："
echo "   1. 运行数据库迁移脚本更新表结构"
echo "   2. 启动应用程序测试功能"
echo "   3. 验证用户管理功能是否正常工作"
echo ""
echo "🚀 启动应用程序："
echo "   mvn spring-boot:run"
