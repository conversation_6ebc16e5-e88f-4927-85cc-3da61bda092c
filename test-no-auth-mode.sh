#!/bin/bash

# 无认证模式验证脚本
# 验证 Spring Security 认证是否已完全移除

echo "🔓 开始验证无认证模式..."

# 配置
BASE_URL="http://localhost:8080"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试结果
TESTS_PASSED=0
TESTS_FAILED=0

# 测试函数
test_no_auth_api() {
    local test_name="$1"
    local method="$2"
    local url="$3"
    local expected_code="$4"
    
    echo -e "\n📋 测试: ${BLUE}$test_name${NC}"
    
    if [ "$method" = "POST" ]; then
        response=$(curl -s -w "\n%{http_code}" -X POST "$url" \
            -H "Content-Type: application/json" \
            -d '{"test": "data"}')
    else
        response=$(curl -s -w "\n%{http_code}" -X GET "$url")
    fi
    
    # 分离响应体和状态码
    body=$(echo "$response" | head -n -1)
    status_code=$(echo "$response" | tail -n 1)
    
    echo "📤 请求: $method $url"
    echo "📥 状态码: $status_code"
    echo "📄 响应: $(echo "$body" | head -c 200)..."
    
    if [ "$status_code" = "$expected_code" ]; then
        echo -e "✅ ${GREEN}测试通过${NC}"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        return 0
    else
        echo -e "❌ ${RED}测试失败${NC} (期望: $expected_code, 实际: $status_code)"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        return 1
    fi
}

echo -e "\n🚀 开始无认证模式验证..."

# 1. 测试管理员接口无需认证
echo -e "\n${YELLOW}=== 1. 管理员接口无认证访问测试 ===${NC}"
test_no_auth_api "无认证访问用户列表" "GET" "$BASE_URL/api/admin/users" "200"
test_no_auth_api "无认证访问用户统计" "GET" "$BASE_URL/api/admin/users/statistics" "200"

# 2. 测试认证接口
echo -e "\n${YELLOW}=== 2. 认证接口测试 ===${NC}"
test_no_auth_api "获取当前用户信息" "GET" "$BASE_URL/api/auth/me" "200"
test_no_auth_api "检查邮箱可用性" "GET" "$BASE_URL/api/auth/check-email?email=<EMAIL>" "200"

# 3. 测试登录接口
echo -e "\n${YELLOW}=== 3. 登录接口测试 ===${NC}"
login_response=$(curl -s -X POST "$BASE_URL/api/auth/login" \
    -H "Content-Type: application/json" \
    -d '{"email":"<EMAIL>","password":"admin123"}')

echo "📋 登录响应: $login_response"

if echo "$login_response" | grep -q '"code":200'; then
    echo -e "✅ ${GREEN}登录接口正常工作${NC}"
    TESTS_PASSED=$((TESTS_PASSED + 1))
    
    # 检查是否返回用户信息而不是Token
    if echo "$login_response" | grep -q '"user"'; then
        echo -e "✅ ${GREEN}返回用户信息而非Token${NC}"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        echo -e "❌ ${RED}仍然返回Token而非用户信息${NC}"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
else
    echo -e "❌ ${RED}登录接口异常${NC}"
    TESTS_FAILED=$((TESTS_FAILED + 1))
fi

# 4. 测试Swagger文档访问
echo -e "\n${YELLOW}=== 4. Swagger文档访问测试 ===${NC}"
test_no_auth_api "Swagger UI访问" "GET" "$BASE_URL/swagger-ui.html" "200"
test_no_auth_api "API文档访问" "GET" "$BASE_URL/v3/api-docs" "200"

# 5. 测试CORS配置
echo -e "\n${YELLOW}=== 5. CORS配置测试 ===${NC}"
cors_response=$(curl -s -w "\n%{http_code}" -X OPTIONS "$BASE_URL/api/admin/users" \
    -H "Origin: http://localhost:3000" \
    -H "Access-Control-Request-Method: GET" \
    -H "Access-Control-Request-Headers: Content-Type")

cors_status=$(echo "$cors_response" | tail -n 1)
if [ "$cors_status" = "200" ]; then
    echo -e "✅ ${GREEN}CORS配置正常${NC}"
    TESTS_PASSED=$((TESTS_PASSED + 1))
else
    echo -e "❌ ${RED}CORS配置异常${NC}"
    TESTS_FAILED=$((TESTS_FAILED + 1))
fi

# 6. 测试错误的接口
echo -e "\n${YELLOW}=== 6. 错误接口测试 ===${NC}"
test_no_auth_api "访问不存在的接口" "GET" "$BASE_URL/api/nonexistent" "404"

# 7. 验证JWT过滤器是否禁用
echo -e "\n${YELLOW}=== 7. JWT过滤器禁用验证 ===${NC}"

# 发送带有无效Token的请求，应该仍然成功（因为过滤器已禁用）
invalid_token_response=$(curl -s -w "\n%{http_code}" -X GET "$BASE_URL/api/admin/users" \
    -H "Authorization: Bearer invalid_token_should_be_ignored")

invalid_token_status=$(echo "$invalid_token_response" | tail -n 1)
if [ "$invalid_token_status" = "200" ]; then
    echo -e "✅ ${GREEN}JWT过滤器已禁用（无效Token被忽略）${NC}"
    TESTS_PASSED=$((TESTS_PASSED + 1))
else
    echo -e "❌ ${RED}JWT过滤器仍然生效${NC}"
    TESTS_FAILED=$((TESTS_FAILED + 1))
fi

# 8. 测试批量操作接口
echo -e "\n${YELLOW}=== 8. 批量操作接口测试 ===${NC}"

# 测试批量操作（通常需要管理员权限）
batch_test_data='[1,2,3]'
batch_response=$(curl -s -w "\n%{http_code}" -X PUT "$BASE_URL/api/admin/users/batch/status" \
    -H "Content-Type: application/json" \
    -d "$batch_test_data")

batch_status=$(echo "$batch_response" | tail -n 1)
if [ "$batch_status" = "200" ] || [ "$batch_status" = "400" ]; then
    # 200表示成功，400表示参数错误但接口可访问
    echo -e "✅ ${GREEN}批量操作接口可访问${NC}"
    TESTS_PASSED=$((TESTS_PASSED + 1))
else
    echo -e "❌ ${RED}批量操作接口访问失败${NC}"
    TESTS_FAILED=$((TESTS_FAILED + 1))
fi

# 9. 测试健康检查
echo -e "\n${YELLOW}=== 9. 健康检查测试 ===${NC}"
test_no_auth_api "应用健康检查" "GET" "$BASE_URL/actuator/health" "200"

# 10. 验证无认证模式特征
echo -e "\n${YELLOW}=== 10. 无认证模式特征验证 ===${NC}"

echo "🔍 验证无认证模式特征..."

# 检查是否所有管理员接口都可以无认证访问
admin_endpoints=(
    "/api/admin/users"
    "/api/admin/users/statistics"
    "/api/auth/me"
)

all_admin_accessible=true
for endpoint in "${admin_endpoints[@]}"; do
    response=$(curl -s -w "%{http_code}" -X GET "$BASE_URL$endpoint")
    status_code="${response: -3}"
    
    if [ "$status_code" = "200" ]; then
        echo "✅ $endpoint 可无认证访问"
    else
        echo "❌ $endpoint 无认证访问失败 (状态码: $status_code)"
        all_admin_accessible=false
    fi
done

if [ "$all_admin_accessible" = true ]; then
    echo -e "✅ ${GREEN}所有管理员接口都可无认证访问${NC}"
    TESTS_PASSED=$((TESTS_PASSED + 1))
else
    echo -e "❌ ${RED}部分管理员接口仍需认证${NC}"
    TESTS_FAILED=$((TESTS_FAILED + 1))
fi

# 测试结果汇总
echo -e "\n${BLUE}===========================================${NC}"
echo -e "${BLUE}           无认证模式验证结果${NC}"
echo -e "${BLUE}===========================================${NC}"
echo -e "✅ ${GREEN}通过测试: $TESTS_PASSED${NC}"
echo -e "❌ ${RED}失败测试: $TESTS_FAILED${NC}"
echo -e "📊 ${BLUE}总计测试: $((TESTS_PASSED + TESTS_FAILED))${NC}"

if [ $TESTS_FAILED -eq 0 ]; then
    echo -e "\n🎉 ${GREEN}所有测试通过！Spring Security 认证已完全移除！${NC}"
    echo -e "\n📋 ${GREEN}无认证模式特性确认：${NC}"
    echo "✅ 所有接口无需认证即可访问"
    echo "✅ JWT 过滤器已禁用"
    echo "✅ 管理员接口开放访问"
    echo "✅ 登录接口返回用户信息而非Token"
    echo "✅ CORS 配置正常工作"
    echo "✅ Swagger 文档可正常访问"
    echo ""
    echo -e "🚀 ${BLUE}系统现在运行在无认证模式下，适合快速开发和测试！${NC}"
    exit 0
else
    echo -e "\n⚠️ ${YELLOW}部分测试失败，请检查以下问题：${NC}"
    echo "1. 后端服务是否正常启动"
    echo "2. Spring Security 配置是否正确修改"
    echo "3. JWT 过滤器是否已禁用"
    echo "4. 数据库连接是否正常"
    echo "5. 接口路径是否正确"
    echo ""
    echo -e "💡 ${BLUE}建议：${NC}"
    echo "- 检查后端日志是否有错误信息"
    echo "- 验证 SecurityConfig.java 配置"
    echo "- 确认 JWT 相关组件已禁用"
    echo "- 重启后端服务后重新测试"
    exit 1
fi
