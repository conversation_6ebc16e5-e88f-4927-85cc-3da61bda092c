/**
 * 登录路由修复脚本
 * 解决前端无法跳转到登录页面的问题
 */

// 修复登录路由问题
function fixLoginRouting() {
  console.log('🔧 开始修复登录路由问题...')
  
  const fixes = {
    routerAccess: false,
    storeAccess: false,
    authStateReset: false,
    navigationTest: false,
    overall: false
  }
  
  try {
    // 1. 确保路由器可访问
    console.log('1️⃣ 检查路由器访问...')
    
    let router = null
    
    // 尝试多种方式获取路由器
    if (window.$router) {
      router = window.$router
    } else if (window.app && window.app.config.globalProperties.$router) {
      router = window.app.config.globalProperties.$router
    } else if (window.Vue && window.Vue.prototype.$router) {
      router = window.Vue.prototype.$router
    }
    
    if (router) {
      fixes.routerAccess = true
      console.log('✅ 路由器访问正常')
      console.log('📋 当前路由:', router.currentRoute.value.path)
      
      // 将路由器暴露到全局，方便调试
      window.$router = router
    } else {
      console.log('❌ 无法访问路由器')
      
      // 尝试从DOM中获取Vue实例
      const appElement = document.getElementById('app')
      if (appElement && appElement.__vue__) {
        router = appElement.__vue__.$router
        if (router) {
          fixes.routerAccess = true
          window.$router = router
          console.log('✅ 从DOM获取路由器成功')
        }
      }
    }
    
    // 2. 确保Store可访问
    console.log('2️⃣ 检查Store访问...')
    
    let store = null
    
    // 尝试多种方式获取Store
    if (window.$store) {
      store = window.$store
    } else if (window.app && window.app.config.globalProperties.$store) {
      store = window.app.config.globalProperties.$store
    } else if (window.Vue && window.Vue.prototype.$store) {
      store = window.Vue.prototype.$store
    }
    
    if (store) {
      fixes.storeAccess = true
      console.log('✅ Store访问正常')
      
      // 将Store暴露到全局，方便调试
      window.$store = store
      
      // 检查认证状态
      try {
        const isAuthenticated = store.getters['auth/isAuthenticated']
        const currentUser = store.getters['auth/currentUser']
        
        console.log('📋 当前认证状态:', {
          isAuthenticated,
          hasUser: !!currentUser,
          userRole: currentUser?.role
        })
        
      } catch (error) {
        console.log('⚠️ 获取认证状态失败:', error.message)
      }
    } else {
      console.log('❌ 无法访问Store')
    }
    
    // 3. 重置认证状态（如果需要）
    console.log('3️⃣ 重置认证状态...')
    
    if (store) {
      try {
        // 清除可能的错误认证状态
        const token = localStorage.getItem('code_combined_token')
        
        if (token) {
          console.log('📋 发现本地Token，检查有效性...')
          
          // 简单检查Token格式
          const parts = token.split('.')
          if (parts.length === 3) {
            console.log('✅ Token格式正确')
            
            // 尝试解析Token
            try {
              const payload = JSON.parse(atob(parts[1]))
              const now = Math.floor(Date.now() / 1000)
              
              if (payload.exp && payload.exp > now) {
                console.log('✅ Token未过期')
              } else {
                console.log('⚠️ Token已过期，清除...')
                localStorage.removeItem('code_combined_token')
                store.commit('auth/CLEAR_AUTH')
              }
            } catch (error) {
              console.log('⚠️ Token解析失败，清除...')
              localStorage.removeItem('code_combined_token')
              store.commit('auth/CLEAR_AUTH')
            }
          } else {
            console.log('⚠️ Token格式错误，清除...')
            localStorage.removeItem('code_combined_token')
            store.commit('auth/CLEAR_AUTH')
          }
        } else {
          console.log('📋 没有本地Token')
        }
        
        fixes.authStateReset = true
        console.log('✅ 认证状态检查完成')
        
      } catch (error) {
        console.log('❌ 重置认证状态失败:', error.message)
      }
    }
    
    // 4. 测试导航功能
    console.log('4️⃣ 测试导航功能...')
    
    if (router) {
      try {
        // 记录当前路径
        const currentPath = router.currentRoute.value.path
        console.log('📋 当前路径:', currentPath)
        
        // 如果不在登录页面，尝试导航
        if (currentPath !== '/login') {
          console.log('📋 尝试导航到登录页面...')
          
          router.push('/login').then(() => {
            console.log('✅ 导航成功')
            fixes.navigationTest = true
            
            // 检查是否真的跳转了
            setTimeout(() => {
              const newPath = router.currentRoute.value.path
              console.log('📋 导航后路径:', newPath)
              
              if (newPath === '/login') {
                console.log('✅ 成功跳转到登录页面')
              } else {
                console.log('⚠️ 跳转可能被拦截，当前路径:', newPath)
              }
            }, 100)
            
          }).catch(error => {
            console.log('❌ 导航失败:', error.message)
            
            // 尝试强制跳转
            console.log('📋 尝试强制跳转...')
            window.location.href = '/login'
          })
        } else {
          console.log('✅ 已在登录页面')
          fixes.navigationTest = true
        }
        
      } catch (error) {
        console.log('❌ 导航测试失败:', error.message)
      }
    }
    
    // 5. 综合评估
    const passedFixes = Object.values(fixes).filter(v => v === true).length
    const totalFixes = Object.keys(fixes).length - 1 // 排除 overall
    fixes.overall = passedFixes >= totalFixes * 0.75 // 75% 通过率
    
    console.log('\n📊 登录路由修复结果:')
    console.log('=' .repeat(50))
    console.log(`路由器访问: ${fixes.routerAccess ? '✅ 正常' : '❌ 异常'}`)
    console.log(`Store访问: ${fixes.storeAccess ? '✅ 正常' : '❌ 异常'}`)
    console.log(`认证状态: ${fixes.authStateReset ? '✅ 已重置' : '❌ 失败'}`)
    console.log(`导航测试: ${fixes.navigationTest ? '✅ 正常' : '❌ 异常'}`)
    console.log('=' .repeat(50))
    console.log(`总体结果: ${fixes.overall ? '✅ 修复成功' : '❌ 仍有问题'} (${passedFixes}/${totalFixes})`)
    
    if (fixes.overall) {
      console.log('\n🎉 登录路由问题已修复！')
      console.log('💡 现在可以尝试:')
      console.log('  - 点击导航栏的登录链接')
      console.log('  - 直接访问 /login 路径')
      console.log('  - 使用 window.$router.push("/login")')
    } else {
      console.log('\n⚠️ 登录路由仍有问题，请检查:')
      if (!fixes.routerAccess) console.log('  - Vue Router 配置和初始化')
      if (!fixes.storeAccess) console.log('  - Vuex Store 配置和初始化')
      if (!fixes.authStateReset) console.log('  - 认证状态管理')
      if (!fixes.navigationTest) console.log('  - 路由导航功能')
    }
    
    return fixes
    
  } catch (error) {
    console.error('❌ 修复过程中发生错误:', error)
    return fixes
  }
}

// 快速跳转到登录页面
function quickLoginNavigation() {
  console.log('🚀 快速跳转到登录页面...')
  
  try {
    // 方法1: 使用路由器
    if (window.$router) {
      console.log('📋 使用路由器跳转...')
      window.$router.push('/login')
        .then(() => console.log('✅ 路由器跳转成功'))
        .catch(err => {
          console.log('❌ 路由器跳转失败:', err.message)
          
          // 方法2: 使用 window.location
          console.log('📋 使用 window.location 跳转...')
          window.location.href = '/login'
        })
    } else {
      // 直接使用 window.location
      console.log('📋 直接使用 window.location 跳转...')
      window.location.href = '/login'
    }
    
  } catch (error) {
    console.error('❌ 快速跳转失败:', error)
    
    // 最后的备用方案
    console.log('📋 使用备用方案...')
    window.location.href = '/login'
  }
}

// 清除认证状态
function clearAuthState() {
  console.log('🧹 清除认证状态...')
  
  try {
    // 清除localStorage
    localStorage.removeItem('code_combined_token')
    localStorage.removeItem('user')
    
    // 清除sessionStorage
    sessionStorage.clear()
    
    // 清除Store状态
    if (window.$store) {
      window.$store.commit('auth/CLEAR_AUTH')
      console.log('✅ Store认证状态已清除')
    }
    
    console.log('✅ 认证状态清除完成')
    
    // 跳转到首页
    if (window.$router) {
      window.$router.push('/')
    } else {
      window.location.href = '/'
    }
    
  } catch (error) {
    console.error('❌ 清除认证状态失败:', error)
  }
}

// 检查页面状态
function checkPageStatus() {
  console.log('📄 检查页面状态...')
  
  console.log('📋 页面信息:', {
    url: window.location.href,
    pathname: window.location.pathname,
    hash: window.location.hash,
    title: document.title,
    readyState: document.readyState
  })
  
  // 检查Vue应用
  const app = document.getElementById('app')
  if (app) {
    console.log('✅ Vue应用容器存在')
    console.log('📋 应用内容:', app.innerHTML.length > 100 ? '已渲染' : '可能未渲染')
  } else {
    console.log('❌ Vue应用容器不存在')
  }
  
  // 检查路由视图
  const routerView = document.querySelector('router-view')
  console.log('📋 路由视图:', routerView ? '存在' : '不存在')
  
  // 检查导航栏
  const navbar = document.querySelector('.navbar')
  console.log('📋 导航栏:', navbar ? '存在' : '不存在')
  
  // 检查登录链接
  const loginLinks = document.querySelectorAll('a[href="/login"]')
  console.log('📋 登录链接数量:', loginLinks.length)
  
  if (loginLinks.length > 0) {
    loginLinks.forEach((link, index) => {
      console.log(`📋 登录链接 ${index + 1}:`, {
        text: link.textContent.trim(),
        visible: link.offsetParent !== null,
        href: link.href
      })
    })
  }
}

// 导出修复函数到全局
window.fixLoginRouting = fixLoginRouting
window.quickLoginNavigation = quickLoginNavigation
window.clearAuthState = clearAuthState
window.checkPageStatus = checkPageStatus

console.log('🔧 登录路由修复工具已加载！')
console.log('📋 可用命令:')
console.log('  window.fixLoginRouting()      - 完整修复登录路由')
console.log('  window.quickLoginNavigation() - 快速跳转到登录页面')
console.log('  window.clearAuthState()       - 清除认证状态')
console.log('  window.checkPageStatus()      - 检查页面状态')
console.log('')
console.log('🚀 快速开始: window.fixLoginRouting()')

// 自动运行基础修复
setTimeout(() => {
  console.log('\n🔧 自动运行基础修复...')
  
  // 检查当前是否在登录页面
  if (window.location.pathname === '/login') {
    console.log('✅ 当前已在登录页面')
  } else {
    console.log('📋 当前不在登录页面，路径:', window.location.pathname)
    
    // 检查是否有明显的问题
    if (!window.$router && !window.app) {
      console.log('⚠️ 检测到Vue应用可能未正确初始化')
      console.log('💡 建议刷新页面或检查控制台错误')
    }
  }
  
}, 1000)
