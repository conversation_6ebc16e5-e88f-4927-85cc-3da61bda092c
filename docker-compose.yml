version: '3.8'

services:
  # MySQL 数据库
  mysql:
    image: mysql:8.0
    container_name: code-combined-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: code_combined
      MYSQL_USER: codecombined
      MYSQL_PASSWORD: codecombined123
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - code-combined-network

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: code-combined-redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - code-combined-network

  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: code-combined-backend
    restart: always
    ports:
      - "8080:8080"
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SPRING_DATASOURCE_URL: **********************************************************************************************************************
      SPRING_DATASOURCE_USERNAME: codecombined
      SPRING_DATASOURCE_PASSWORD: codecombined123
      SPRING_REDIS_HOST: redis
      SPRING_REDIS_PORT: 6379
    depends_on:
      - mysql
      - redis
    networks:
      - code-combined-network

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: code-combined-frontend
    restart: always
    ports:
      - "80:80"
    depends_on:
      - backend
    networks:
      - code-combined-network

volumes:
  mysql_data:
  redis_data:

networks:
  code-combined-network:
    driver: bridge
