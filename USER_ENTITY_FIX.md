# User实体类字段修复指南

## 🐛 问题描述

编译错误：`cannot find symbol: method getPoints()`

这个错误表明User实体类中缺少`points`字段的getter方法。

## 🔧 解决方案

### 1. 已添加的字段

在User.java中已添加以下字段：

```java
/**
 * 用户积分
 */
private Integer points;

/**
 * 个人简介
 */
private String bio;

/**
 * 所在地
 */
private String location;

/**
 * 公司
 */
private String company;

/**
 * GitHub链接
 */
private String github;

/**
 * 个人网站
 */
private String website;
```

### 2. 数据库迁移脚本

已创建数据库迁移脚本：`V2__Add_User_Extended_Fields.sql`

```sql
-- 添加用户积分字段
ALTER TABLE `user` ADD COLUMN `points` INT DEFAULT 100 COMMENT '用户积分' AFTER `email_verified`;

-- 添加个人简介字段
ALTER TABLE `user` ADD COLUMN `bio` VARCHAR(500) DEFAULT NULL COMMENT '个人简介' AFTER `points`;

-- 添加所在地字段
ALTER TABLE `user` ADD COLUMN `location` VARCHAR(100) DEFAULT NULL COMMENT '所在地' AFTER `bio`;

-- 添加公司字段
ALTER TABLE `user` ADD COLUMN `company` VARCHAR(100) DEFAULT NULL COMMENT '公司' AFTER `location`;

-- 添加GitHub链接字段
ALTER TABLE `user` ADD COLUMN `github` VARCHAR(200) DEFAULT NULL COMMENT 'GitHub链接' AFTER `company`;

-- 添加个人网站字段
ALTER TABLE `user` ADD COLUMN `website` VARCHAR(200) DEFAULT NULL COMMENT '个人网站' AFTER `github`;
```

### 3. Lombok配置验证

User实体类使用了正确的Lombok注解：

```java
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("user")
public class User {
    // 字段定义...
}
```

`@Data`注解会自动生成：
- 所有字段的getter方法
- 所有非final字段的setter方法
- toString()方法
- equals()和hashCode()方法

## 🚀 解决步骤

### 1. 清理和重新编译

```bash
# 进入后端项目目录
cd code-combined-backend

# 清理项目
mvn clean

# 重新编译
mvn compile

# 或者直接运行
mvn spring-boot:run
```

### 2. 检查IDE配置

如果使用IDE（如IntelliJ IDEA或Eclipse），确保：

1. **Lombok插件已安装**
2. **注解处理已启用**
3. **项目已重新导入**

**IntelliJ IDEA**：
- File → Settings → Plugins → 搜索"Lombok"并安装
- File → Settings → Build → Compiler → Annotation Processors → 勾选"Enable annotation processing"

**Eclipse**：
- Help → Eclipse Marketplace → 搜索"Lombok"并安装
- 重启Eclipse

### 3. 验证字段访问

创建测试验证字段是否可访问：

```java
@Test
public void testUserFields() {
    User user = new User();
    
    // 测试points字段
    user.setPoints(100);
    assertEquals(Integer.valueOf(100), user.getPoints());
    
    // 测试其他新字段
    user.setBio("测试简介");
    assertEquals("测试简介", user.getBio());
    
    user.setLocation("北京");
    assertEquals("北京", user.getLocation());
    
    user.setCompany("测试公司");
    assertEquals("测试公司", user.getCompany());
    
    user.setGithub("https://github.com/test");
    assertEquals("https://github.com/test", user.getGithub());
    
    user.setWebsite("https://test.com");
    assertEquals("https://test.com", user.getWebsite());
}
```

### 4. 运行数据库迁移

确保数据库表结构已更新：

```sql
-- 检查表结构
DESCRIBE user;

-- 应该看到新添加的字段：
-- points, bio, location, company, github, website
```

## 🔍 故障排除

### 问题1：Lombok不工作

**症状**：编译时找不到getter/setter方法

**解决方案**：
1. 检查Lombok依赖是否正确添加到pom.xml
2. 确保IDE安装了Lombok插件
3. 重新导入项目
4. 清理并重新编译

### 问题2：数据库字段不存在

**症状**：运行时SQL错误，字段不存在

**解决方案**：
1. 运行数据库迁移脚本
2. 检查数据库连接配置
3. 手动执行ALTER TABLE语句

### 问题3：字段映射错误

**症状**：MyBatis映射错误

**解决方案**：
1. 检查@TableField注解
2. 确保字段名与数据库列名一致
3. 检查MyBatis配置

## 📋 验证清单

- [ ] User.java中已添加所有新字段
- [ ] @Data注解存在且正确
- [ ] Lombok插件已安装并启用
- [ ] 项目已清理并重新编译
- [ ] 数据库迁移脚本已执行
- [ ] 数据库表结构已更新
- [ ] 测试用例通过

## 🎯 完整的User实体类字段列表

```java
public class User {
    private Long id;                    // 用户ID
    private String username;            // 用户名
    private String email;               // 邮箱
    private String password;            // 密码
    private String nickname;            // 昵称
    private String avatar;              // 头像URL
    private String role;                // 角色
    private Integer status;             // 状态
    private Integer emailVerified;      // 邮箱验证状态
    private Integer points;             // 用户积分 ✅ 新增
    private String bio;                 // 个人简介 ✅ 新增
    private String location;            // 所在地 ✅ 新增
    private String company;             // 公司 ✅ 新增
    private String github;              // GitHub链接 ✅ 新增
    private String website;             // 个人网站 ✅ 新增
    private LocalDateTime lastLoginTime; // 最后登录时间
    private String lastLoginIp;         // 最后登录IP
    private LocalDateTime createdTime;  // 创建时间
    private LocalDateTime updatedTime;  // 更新时间
    private Integer deleted;            // 逻辑删除标记
}
```

## 🔄 后续步骤

1. **重新编译项目**
2. **运行数据库迁移**
3. **测试用户管理功能**
4. **验证前端集成**

---

**总结**：User实体类已添加所有必要的字段，包括points、bio、location、company、github、website等。通过Lombok的@Data注解，这些字段的getter/setter方法会自动生成。如果仍有编译错误，请按照故障排除步骤检查Lombok配置和项目编译状态。
