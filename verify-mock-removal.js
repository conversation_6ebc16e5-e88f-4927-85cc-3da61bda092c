/**
 * Mock 数据清理验证脚本
 * 验证前端项目中的所有 mock 数据是否已完全删除
 */

// 验证 Mock 数据清理
function verifyMockRemoval() {
  console.log('🔍 开始验证 Mock 数据清理...')
  
  const results = {
    mockFunctions: false,
    mockFiles: false,
    mockImports: false,
    mockConfig: false,
    overall: false
  }
  
  try {
    // 1. 验证 Mock 函数已移除
    console.log('1️⃣ 验证 Mock 函数已移除...')
    const mockFunctions = [
      'getUserList', 'getUserDetail', 'createUser', 'updateUser', 'deleteUser',
      'batchDeleteUsers', 'toggleUserStatus', 'batchToggleUserStatus',
      'resetUserPassword', 'getUserStatistics', 'exportUsers',
      'testAdmin', 'fixAdmin', 'loginAsAdmin', 'quickLoginAsAdmin', 'quickLoginAsUser',
      'mockLogin', 'generateMockUsers', 'generateMockProblems', 'generateMockProblemSets'
    ]
    
    const existingMockFunctions = mockFunctions.filter(fn => typeof window[fn] === 'function')
    
    if (existingMockFunctions.length === 0) {
      results.mockFunctions = true
      console.log('✅ Mock 函数已完全移除')
    } else {
      console.log('❌ 仍存在 Mock 函数:', existingMockFunctions)
    }
    
    // 2. 验证 Mock 文件已删除
    console.log('2️⃣ 验证 Mock 文件已删除...')
    
    // 检查是否还有对已删除文件的引用
    const mockFileReferences = []
    
    // 检查是否有 mockData 的引用
    if (document.querySelector('script[src*="mockData"]')) {
      mockFileReferences.push('mockData.js 引用')
    }
    
    // 检查是否有 adminTest 的引用
    if (document.querySelector('script[src*="adminTest"]')) {
      mockFileReferences.push('adminTest.js 引用')
    }
    
    if (mockFileReferences.length === 0) {
      results.mockFiles = true
      console.log('✅ Mock 文件引用已清理')
    } else {
      console.log('❌ 仍存在 Mock 文件引用:', mockFileReferences)
    }
    
    // 3. 验证 Mock 导入已移除
    console.log('3️⃣ 验证 Mock 导入已移除...')
    
    // 检查全局对象上是否还有 mock 相关的属性
    const mockProperties = []
    
    for (const prop in window) {
      if (prop.toLowerCase().includes('mock') || 
          prop.toLowerCase().includes('test') && prop.includes('admin')) {
        mockProperties.push(prop)
      }
    }
    
    // 过滤掉系统属性
    const filteredMockProperties = mockProperties.filter(prop => 
      !prop.startsWith('webkit') && 
      !prop.startsWith('moz') && 
      !prop.startsWith('ms') &&
      prop !== 'test' // 排除原生 test 方法
    )
    
    if (filteredMockProperties.length === 0) {
      results.mockImports = true
      console.log('✅ Mock 导入已清理')
    } else {
      console.log('❌ 仍存在 Mock 相关属性:', filteredMockProperties)
    }
    
    // 4. 验证 Mock 配置已移除
    console.log('4️⃣ 验证 Mock 配置已移除...')
    
    // 检查是否还有开发环境的 mock 判断
    let hasMockConfig = false
    
    // 检查是否有 vite-plugin-mock 的痕迹
    if (window.__VITE_PLUGIN_MOCK__) {
      hasMockConfig = true
      console.log('❌ 仍存在 Vite Mock 插件配置')
    }
    
    if (!hasMockConfig) {
      results.mockConfig = true
      console.log('✅ Mock 配置已清理')
    }
    
    // 5. 综合评估
    const passedTests = Object.values(results).filter(v => v === true).length
    const totalTests = Object.keys(results).length - 1 // 排除 overall
    results.overall = passedTests === totalTests
    
    console.log('\n📊 Mock 数据清理验证结果:')
    console.log('=' .repeat(50))
    console.log(`Mock 函数清理: ${results.mockFunctions ? '✅ 通过' : '❌ 失败'}`)
    console.log(`Mock 文件清理: ${results.mockFiles ? '✅ 通过' : '❌ 失败'}`)
    console.log(`Mock 导入清理: ${results.mockImports ? '✅ 通过' : '❌ 失败'}`)
    console.log(`Mock 配置清理: ${results.mockConfig ? '✅ 通过' : '❌ 失败'}`)
    console.log('=' .repeat(50))
    console.log(`总体结果: ${results.overall ? '✅ 清理完成' : '❌ 存在问题'} (${passedTests}/${totalTests})`)
    
    if (results.overall) {
      console.log('\n🎉 恭喜！Mock 数据已完全清理！')
      console.log('📋 清理成果:')
      console.log('  ✅ 所有 Mock 函数已移除')
      console.log('  ✅ 所有 Mock 文件已删除')
      console.log('  ✅ 所有 Mock 导入已清理')
      console.log('  ✅ 所有 Mock 配置已移除')
      console.log('  ✅ 前端现在完全依赖后端 API')
    } else {
      console.log('\n⚠️ Mock 数据清理存在问题，请检查:')
      if (!results.mockFunctions) console.log('  - 清理残留的 Mock 函数')
      if (!results.mockFiles) console.log('  - 删除 Mock 文件引用')
      if (!results.mockImports) console.log('  - 清理 Mock 导入语句')
      if (!results.mockConfig) console.log('  - 移除 Mock 配置')
    }
    
    return results
    
  } catch (error) {
    console.error('❌ 验证过程中发生错误:', error)
    return results
  }
}

// 检查前端依赖
function checkFrontendDependencies() {
  console.log('\n🔧 检查前端依赖状态...')
  
  try {
    // 检查是否有 mock 相关的全局变量
    const mockRelatedGlobals = []
    
    if (typeof window.Mock !== 'undefined') {
      mockRelatedGlobals.push('Mock (mockjs)')
    }
    
    if (typeof window.mockjs !== 'undefined') {
      mockRelatedGlobals.push('mockjs')
    }
    
    if (mockRelatedGlobals.length === 0) {
      console.log('✅ 没有检测到 Mock 相关的全局变量')
    } else {
      console.log('❌ 检测到 Mock 相关的全局变量:', mockRelatedGlobals)
    }
    
    // 检查 API 调用方式
    if (typeof window.request !== 'undefined' || typeof window.api !== 'undefined') {
      console.log('✅ 检测到 API 调用工具')
    } else {
      console.log('⚠️ 未检测到 API 调用工具，请确保正确配置')
    }
    
  } catch (error) {
    console.error('❌ 检查依赖时发生错误:', error)
  }
}

// 提供清理建议
function showCleanupSuggestions() {
  console.log('\n🔧 清理建议:')
  console.log('')
  console.log('1. 如果仍有 Mock 函数残留:')
  console.log('   - 检查浏览器缓存，强制刷新页面 (Ctrl+F5)')
  console.log('   - 清理浏览器存储 (localStorage, sessionStorage)')
  console.log('   - 重启开发服务器')
  console.log('')
  console.log('2. 如果仍有 Mock 文件引用:')
  console.log('   - 搜索项目中的 "mockData" 和 "adminTest" 引用')
  console.log('   - 检查 HTML 文件中的 script 标签')
  console.log('   - 清理 IDE 缓存并重新索引')
  console.log('')
  console.log('3. 如果仍有 Mock 配置:')
  console.log('   - 检查 vite.config.js 文件')
  console.log('   - 检查 package.json 中的依赖')
  console.log('   - 重新安装依赖: npm install')
  console.log('')
  console.log('4. 完全清理步骤:')
  console.log('   cd frontend')
  console.log('   rm -rf node_modules package-lock.json')
  console.log('   npm install')
  console.log('   npm run dev')
}

// 测试 API 连接
async function testAPIConnection() {
  console.log('\n🌐 测试 API 连接...')
  
  try {
    // 测试后端健康检查
    const healthResponse = await fetch('/actuator/health')
    if (healthResponse.ok) {
      console.log('✅ 后端服务连接正常')
    } else {
      console.log('❌ 后端服务连接失败，状态码:', healthResponse.status)
    }
    
    // 测试 API 基础路径
    const apiResponse = await fetch('/api/admin/users/statistics', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
      }
    })
    
    if (apiResponse.ok) {
      console.log('✅ API 接口连接正常')
    } else if (apiResponse.status === 401) {
      console.log('⚠️ API 需要认证（正常）')
    } else {
      console.log('❌ API 接口连接异常，状态码:', apiResponse.status)
    }
    
  } catch (error) {
    console.error('❌ API 连接测试失败:', error.message)
    console.log('💡 请确保后端服务已启动：mvn spring-boot:run')
  }
}

// 导出验证函数到全局
window.verifyMockRemoval = verifyMockRemoval
window.checkFrontendDependencies = checkFrontendDependencies
window.showCleanupSuggestions = showCleanupSuggestions
window.testAPIConnection = testAPIConnection

console.log('🔍 Mock 数据清理验证工具已加载！')
console.log('📋 可用命令:')
console.log('  window.verifyMockRemoval()        - 验证 Mock 数据清理')
console.log('  window.checkFrontendDependencies() - 检查前端依赖')
console.log('  window.showCleanupSuggestions()   - 显示清理建议')
console.log('  window.testAPIConnection()        - 测试 API 连接')
console.log('')
console.log('🚀 快速开始: window.verifyMockRemoval()')

// 自动运行基础验证
setTimeout(() => {
  console.log('\n🔍 自动运行基础验证...')
  
  // 检查关键 Mock 函数
  const keyMockFunctions = ['getUserList', 'mockLogin', 'testAdmin', 'loginAsAdmin']
  const existingKeyFunctions = keyMockFunctions.filter(fn => typeof window[fn] === 'function')
  
  if (existingKeyFunctions.length === 0) {
    console.log('✅ 关键 Mock 函数已清理')
  } else {
    console.log('⚠️ 检测到关键 Mock 函数仍然存在:', existingKeyFunctions)
    console.log('建议运行完整验证: window.verifyMockRemoval()')
  }
  
  // 检查认证状态
  const token = localStorage.getItem('token')
  if (token) {
    console.log('✅ 检测到认证 Token')
  } else {
    console.log('⚠️ 未检测到认证 Token，请先登录')
  }
  
}, 1000)
