#!/bin/bash

# Vue 组件转换脚本
# 将 Vue 2 Options API 转换为 Vue 3 Composition API + TypeScript

echo "🔄 开始转换 Vue 组件到 Vue 3 + TypeScript..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 转换结果
CONVERTED_FILES=0
SKIPPED_FILES=0
ERROR_FILES=0

# 检查 Vue 组件是否需要转换
check_vue_component() {
    local vue_file="$1"
    
    if [ ! -f "$vue_file" ]; then
        return 1
    fi
    
    # 检查是否已经是 Composition API + TypeScript
    if grep -q "<script setup lang=\"ts\">" "$vue_file"; then
        return 1  # 已经转换过
    fi
    
    # 检查是否包含 <script> 标签
    if grep -q "<script>" "$vue_file"; then
        return 0  # 需要转换
    fi
    
    return 1  # 不需要转换
}

# 转换 Vue 组件
convert_vue_component() {
    local vue_file="$1"
    local backup_file="${vue_file}.backup"
    
    echo -e "\n📋 转换: ${BLUE}$vue_file${NC}"
    
    # 创建备份
    cp "$vue_file" "$backup_file"
    
    # 基础转换：将 <script> 改为 <script setup lang="ts">
    sed -i.tmp 's/<script>/<script setup lang="ts">/' "$vue_file"
    
    # 移除 export default 和相关结构
    sed -i.tmp '/export default {/,/^}$/d' "$vue_file"
    
    # 添加基本的 TypeScript 导入
    if ! grep -q "import.*vue" "$vue_file"; then
        sed -i.tmp '1i\
import { ref, computed, onMounted, onUnmounted, watch } from '\''vue'\''\
import { useStore } from '\''vuex'\''\
import { useRouter } from '\''vue-router'\''\
import type { } from '\''@/types'\''
' "$vue_file"
    fi
    
    # 清理临时文件
    rm -f "${vue_file}.tmp"
    
    echo -e "✅ ${GREEN}基础转换完成${NC}"
    echo -e "⚠️ ${YELLOW}需要手动完成以下步骤：${NC}"
    echo "   1. 添加 defineOptions({ name: 'ComponentName' })"
    echo "   2. 定义 Props 和 Emits 接口"
    echo "   3. 使用 defineProps 和 defineEmits"
    echo "   4. 转换 data() 为 ref/reactive"
    echo "   5. 转换 computed 属性"
    echo "   6. 转换 methods 为函数"
    echo "   7. 添加类型注解"
    
    CONVERTED_FILES=$((CONVERTED_FILES + 1))
}

# 查找所有 Vue 组件
echo -e "\n${YELLOW}=== 1. 扫描 Vue 组件 ===${NC}"

vue_files=()
if [ -d "frontend/src" ]; then
    while IFS= read -r -d '' vue_file; do
        vue_files+=("$vue_file")
    done < <(find frontend/src -name "*.vue" -print0)
fi

echo "📊 找到 ${#vue_files[@]} 个 Vue 组件文件"

# 检查需要转换的组件
echo -e "\n${YELLOW}=== 2. 检查转换需求 ===${NC}"

need_conversion=()
already_converted=()

for vue_file in "${vue_files[@]}"; do
    if check_vue_component "$vue_file"; then
        need_conversion+=("$vue_file")
        echo -e "📋 需要转换: ${BLUE}$vue_file${NC}"
    else
        already_converted+=("$vue_file")
        echo -e "✅ 已转换: ${GREEN}$vue_file${NC}"
    fi
done

echo -e "\n📊 转换统计:"
echo "   需要转换: ${#need_conversion[@]} 个"
echo "   已经转换: ${#already_converted[@]} 个"

# 执行转换
if [ ${#need_conversion[@]} -gt 0 ]; then
    echo -e "\n${YELLOW}=== 3. 执行转换 ===${NC}"
    
    for vue_file in "${need_conversion[@]}"; do
        convert_vue_component "$vue_file"
    done
else
    echo -e "\n${GREEN}所有组件都已转换完成！${NC}"
fi

# 生成转换指南
echo -e "\n${YELLOW}=== 4. 生成转换指南 ===${NC}"

cat > "VUE_COMPONENT_CONVERSION_GUIDE.md" << 'EOF'
# Vue 组件转换指南

## 转换步骤

### 1. 基础结构转换

```vue
<!-- 转换前 -->
<script>
export default {
  name: 'MyComponent',
  // ...
}
</script>

<!-- 转换后 -->
<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import type { } from '@/types'

defineOptions({
  name: 'MyComponent'
})
</script>
```

### 2. Props 转换

```vue
<!-- 转换前 -->
<script>
export default {
  props: {
    title: String,
    count: Number,
    items: Array
  }
}
</script>

<!-- 转换后 -->
<script setup lang="ts">
interface Props {
  title: string
  count?: number
  items: Item[]
}

const props = withDefaults(defineProps<Props>(), {
  count: 0
})
</script>
```

### 3. Emits 转换

```vue
<!-- 转换前 -->
<script>
export default {
  emits: ['update', 'delete']
}
</script>

<!-- 转换后 -->
<script setup lang="ts">
interface Emits {
  (e: 'update', value: string): void
  (e: 'delete', id: number): void
}

const emit = defineEmits<Emits>()
</script>
```

### 4. Data 转换

```vue
<!-- 转换前 -->
<script>
export default {
  data() {
    return {
      loading: false,
      items: [],
      user: null
    }
  }
}
</script>

<!-- 转换后 -->
<script setup lang="ts">
const loading = ref<boolean>(false)
const items = ref<Item[]>([])
const user = ref<User | null>(null)
</script>
```

### 5. Computed 转换

```vue
<!-- 转换前 -->
<script>
export default {
  computed: {
    filteredItems() {
      return this.items.filter(item => item.active)
    }
  }
}
</script>

<!-- 转换后 -->
<script setup lang="ts">
const filteredItems = computed((): Item[] => 
  items.value.filter((item: Item) => item.active)
)
</script>
```

### 6. Methods 转换

```vue
<!-- 转换前 -->
<script>
export default {
  methods: {
    handleClick() {
      this.loading = true
    },
    async fetchData() {
      // ...
    }
  }
}
</script>

<!-- 转换后 -->
<script setup lang="ts">
const handleClick = (): void => {
  loading.value = true
}

const fetchData = async (): Promise<void> => {
  // ...
}
</script>
```

### 7. 生命周期转换

```vue
<!-- 转换前 -->
<script>
export default {
  mounted() {
    this.fetchData()
  },
  unmounted() {
    this.cleanup()
  }
}
</script>

<!-- 转换后 -->
<script setup lang="ts">
onMounted(() => {
  fetchData()
})

onUnmounted(() => {
  cleanup()
})
</script>
```

### 8. Watch 转换

```vue
<!-- 转换前 -->
<script>
export default {
  watch: {
    searchKeyword(newVal) {
      this.search(newVal)
    }
  }
}
</script>

<!-- 转换后 -->
<script setup lang="ts">
watch(searchKeyword, (newVal: string) => {
  search(newVal)
})
</script>
```

## 常见类型定义

```typescript
// 组件 Props
interface Props {
  title: string
  count?: number
  items: Item[]
  user: User | null
}

// 组件 Emits
interface Emits {
  (e: 'update', value: string): void
  (e: 'delete', id: number): void
  (e: 'change', data: { field: string; value: any }): void
}

// 响应式数据
const loading = ref<boolean>(false)
const items = ref<Item[]>([])
const form = reactive<FormData>({
  name: '',
  email: ''
})

// 计算属性
const filteredItems = computed((): Item[] => {
  return items.value.filter(item => item.active)
})

// 方法
const handleSubmit = async (): Promise<void> => {
  // ...
}
```
EOF

echo -e "✅ ${GREEN}转换指南已生成: VUE_COMPONENT_CONVERSION_GUIDE.md${NC}"

# 生成转换报告
echo -e "\n${BLUE}===========================================${NC}"
echo -e "${BLUE}           Vue 组件转换报告${NC}"
echo -e "${BLUE}===========================================${NC}"
echo -e "📊 ${BLUE}总计组件: ${#vue_files[@]} 个${NC}"
echo -e "✅ ${GREEN}已转换组件: ${#already_converted[@]} 个${NC}"
echo -e "🔄 ${YELLOW}本次转换: $CONVERTED_FILES 个${NC}"
echo -e "⚠️ ${YELLOW}跳过转换: $SKIPPED_FILES 个${NC}"
echo -e "❌ ${RED}转换失败: $ERROR_FILES 个${NC}"

# 下一步建议
echo -e "\n${BLUE}下一步操作建议：${NC}"

if [ $CONVERTED_FILES -gt 0 ]; then
    echo "1. 手动完善转换后的组件："
    echo "   - 添加类型定义"
    echo "   - 完善 Props 和 Emits 接口"
    echo "   - 转换复杂的业务逻辑"
    echo ""
fi

echo "2. 运行类型检查："
echo "   cd frontend && npm run type-check"
echo ""

echo "3. 启动开发服务器测试："
echo "   cd frontend && npm run dev"
echo ""

echo "4. 参考转换示例："
echo "   - QuickAddButton.vue (已完成)"
echo "   - AddProblemModal.vue (已完成)"
echo ""

echo "5. 查看转换指南："
echo "   cat VUE_COMPONENT_CONVERSION_GUIDE.md"

if [ $CONVERTED_FILES -eq 0 ] && [ ${#need_conversion[@]} -eq 0 ]; then
    echo -e "\n🎉 ${GREEN}恭喜！所有 Vue 组件都已转换为 Vue 3 + TypeScript 格式！${NC}"
    echo -e "🚀 ${BLUE}现在可以享受 Composition API 和 TypeScript 带来的开发体验提升！${NC}"
else
    echo -e "\n💡 ${YELLOW}转换进行中，请根据上述建议继续完善。${NC}"
fi

exit 0
