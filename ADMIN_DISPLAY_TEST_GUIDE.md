# 管理员显示和操作测试指南

## 🎯 测试目标

验证管理员用户能够正确显示和操作管理页面的所有功能。

## 🔧 已修复的问题

### 1. 角色值统一
- ✅ 修复了 `frontend/mock/auth.js` 中的角色值不一致问题
- ✅ 管理员角色：`'admin'`（小写）
- ✅ 普通用户角色：`'user'`（小写）
- ✅ 确保所有组件使用统一的角色检查逻辑

### 2. Mock 数据完善
- ✅ 创建了完整的 Mock 数据系统
- ✅ 添加了快速登录功能
- ✅ 统一了用户数据格式

### 3. 组件完整性
- ✅ 创建了所有缺失的模态框组件
- ✅ 解决了 Vite 导入错误
- ✅ 完善了管理功能模块

## 🚀 完整测试流程

### 步骤 1：清理环境
```bash
# 清理浏览器缓存
localStorage.clear()
sessionStorage.clear()

# 重启开发服务器
cd frontend
npm run dev
```

### 步骤 2：管理员登录测试

**方法 1：使用预设管理员账号**
1. 访问：`http://localhost:3000/login`
2. 输入管理员账号：
   - 邮箱：`<EMAIL>`
   - 密码：`admin123`
3. 点击登录

**方法 2：使用快速登录（如果可用）**
1. 在登录页面查找快速登录按钮
2. 点击红色的"管理员"按钮

**方法 3：控制台命令**
```javascript
// 在浏览器控制台执行
window.quickLoginAsAdmin && window.quickLoginAsAdmin()
```

### 步骤 3：验证登录状态
登录成功后，在控制台执行：
```javascript
// 检查用户信息
console.log('当前用户:', this.$store?.getters?.['auth/currentUser'])
console.log('用户角色:', this.$store?.getters?.['auth/currentUser']?.role)
console.log('是否管理员:', this.$store?.getters?.['auth/isAdmin'])
console.log('认证状态:', this.$store?.getters?.['auth/isAuthenticated'])
```

**预期结果**：
- 当前用户：包含完整用户信息
- 用户角色：`'admin'`
- 是否管理员：`true`
- 认证状态：`true`

### 步骤 4：检查管理员入口显示

在用户控制台页面（`/dashboard`）应该能看到：

**✅ 页面顶部**：
- 黄色的"管理控制台"按钮

**✅ 管理员权限提示卡片**：
- 红色边框的权限提示卡片
- "系统管理员权限"标题
- "进入管理控制台"按钮

**✅ 快速操作区域**：
- 红色的"系统管理控制台"按钮
- 包含功能描述文字

**✅ 个人信息卡片**：
- 角色显示为"系统管理员"（红色徽章）
- "管理控制台"按钮

**✅ 导航栏**：
- 用户头像下拉菜单中的"管理控制台"选项

### 步骤 5：访问管理平台

**测试所有入口**：
1. 点击页面顶部的黄色"管理控制台"按钮
2. 点击权限提示卡片中的"进入管理控制台"按钮
3. 点击快速操作区域的管理控制台按钮
4. 点击个人信息卡片中的管理按钮
5. 点击导航栏下拉菜单中的管理入口

**预期结果**：
- ✅ 成功跳转到 `/admin` 路径
- ✅ 看到管理平台界面
- ✅ 侧边栏显示各种管理功能

### 步骤 6：测试管理功能模块

**6.1 数据概览页面**
- ✅ 统计卡片显示正常
- ✅ 最近活动时间线显示
- ✅ 快速操作按钮可用
- ✅ 数据实时更新

**6.2 用户管理功能**
- ✅ 用户列表加载正常
- ✅ 搜索和筛选功能工作
- ✅ 分页功能正常
- ✅ 创建用户模态框打开
- ✅ 编辑用户功能可用
- ✅ 查看用户详情正常
- ✅ 批量操作功能工作

**6.3 题目管理功能**
- ✅ 题目列表显示正常
- ✅ 难度和状态筛选工作
- ✅ 创建题目模态框打开
- ✅ 编辑题目功能可用
- ✅ 查看题目详情正常
- ✅ 状态管理功能工作

**6.4 题集管理功能**
- ✅ 题集列表显示正常
- ✅ 可见性控制工作
- ✅ 创建题集模态框打开
- ✅ 编辑题集功能可用
- ✅ 查看题集详情正常
- ✅ 题目管理功能工作

**6.5 系统设置功能**
- ✅ 基本设置页面加载
- ✅ 用户设置功能可用
- ✅ 题目设置功能可用
- ✅ 系统维护功能显示

### 步骤 7：测试模态框操作

**7.1 用户管理模态框**
```javascript
// 测试创建用户
1. 点击"创建用户"按钮
2. 填写用户信息
3. 点击"创建用户"保存
4. 验证用户列表更新

// 测试编辑用户
1. 点击用户列表中的编辑按钮
2. 修改用户信息
3. 点击"保存修改"
4. 验证更改生效

// 测试查看用户详情
1. 点击用户列表中的查看按钮
2. 验证详情信息显示完整
3. 检查活动记录和统计数据
```

**7.2 题目管理模态框**
```javascript
// 测试创建题目
1. 点击"创建题目"按钮
2. 填写题目信息和示例
3. 设置难度和标签
4. 点击"创建题目"保存

// 测试编辑题目
1. 点击题目列表中的编辑按钮
2. 修改题目信息
3. 添加或删除示例
4. 点击"保存修改"
```

**7.3 题集管理模态框**
```javascript
// 测试创建题集
1. 点击"创建题集"按钮
2. 填写题集基本信息
3. 设置可见性和状态
4. 点击"创建题集"保存

// 测试题集题目管理
1. 点击题集列表中的"管理题目"按钮
2. 从右侧添加题目到题集
3. 调整题目顺序
4. 移除不需要的题目
5. 点击"保存更改"
```

## 🐛 常见问题排查

### 问题 1：看不到管理员入口
**检查步骤**：
```javascript
// 1. 检查用户信息
const user = this.$store.getters['auth/currentUser']
console.log('用户信息:', user)
console.log('用户角色:', user?.role)

// 2. 检查角色类型
console.log('角色类型:', typeof user?.role)
console.log('是否为admin:', user?.role === 'admin')

// 3. 检查认证状态
console.log('认证状态:', this.$store.getters['auth/isAuthenticated'])
```

**解决方案**：
```javascript
// 如果角色不正确，手动设置
if (user && user.role !== 'admin') {
  user.role = 'admin'
  this.$store.commit('auth/SET_USER', user)
  location.reload()
}
```

### 问题 2：无法访问 /admin 路径
**检查步骤**：
```javascript
// 检查路由守卫条件
const isAuthenticated = this.$store.getters['auth/isAuthenticated']
const currentUser = this.$store.getters['auth/currentUser']

console.log('认证状态:', isAuthenticated)
console.log('当前用户:', currentUser)
console.log('管理员检查:', currentUser?.role === 'admin')
```

**解决方案**：
```javascript
// 确保所有条件都满足
if (!isAuthenticated || !currentUser || currentUser.role !== 'admin') {
  // 重新登录为管理员
  window.location.href = '/login'
}
```

### 问题 3：模态框无法打开
**检查步骤**：
```javascript
// 检查组件是否正确导入
console.log('组件列表:', this.$options.components)

// 检查模态框状态
console.log('模态框状态:', this.showCreateModal, this.showEditModal)
```

**解决方案**：
```javascript
// 手动触发模态框
this.showCreateModal = true
this.$nextTick(() => {
  console.log('模态框应该已显示')
})
```

### 问题 4：Mock 数据不显示
**检查步骤**：
```javascript
// 检查数据加载
console.log('用户列表:', this.users)
console.log('题目列表:', this.problems)
console.log('题集列表:', this.problemSets)
```

**解决方案**：
```javascript
// 手动重新加载数据
this.loadUsers()
this.loadProblems()
this.loadProblemSets()
```

## ✅ 成功标准

测试成功的标准是：

### 1. 登录和权限
- ✅ 管理员能够成功登录
- ✅ 用户角色正确识别为 `'admin'`
- ✅ 权限检查通过

### 2. 界面显示
- ✅ 控制台页面显示所有管理员入口
- ✅ 管理员权限提示卡片显示
- ✅ 角色标识显示为"系统管理员"

### 3. 功能访问
- ✅ 所有管理入口都能正确跳转
- ✅ 管理平台页面正常加载
- ✅ 侧边栏导航工作正常

### 4. 模块功能
- ✅ 数据概览页面显示正常
- ✅ 用户管理功能完全可用
- ✅ 题目管理功能完全可用
- ✅ 题集管理功能完全可用
- ✅ 系统设置功能可用

### 5. 模态框操作
- ✅ 所有模态框能正常打开
- ✅ 表单验证工作正常
- ✅ 数据保存和更新功能正常
- ✅ Mock 数据显示正确

## 🎯 快速验证命令

在浏览器控制台执行以下命令进行快速验证：

```javascript
// 快速验证脚本
(function() {
  console.log('=== 管理员权限验证 ===')
  
  // 检查用户信息
  const user = this.$store?.getters?.['auth/currentUser']
  console.log('1. 用户信息:', user)
  console.log('2. 用户角色:', user?.role)
  console.log('3. 是否管理员:', user?.role === 'admin')
  console.log('4. 认证状态:', this.$store?.getters?.['auth/isAuthenticated'])
  
  // 检查页面元素
  const adminButtons = document.querySelectorAll('[href="/admin"], [onclick*="admin"]')
  console.log('5. 管理入口数量:', adminButtons.length)
  
  // 检查路由
  console.log('6. 当前路径:', window.location.pathname)
  
  if (user?.role === 'admin' && adminButtons.length > 0) {
    console.log('✅ 管理员权限验证通过！')
  } else {
    console.log('❌ 管理员权限验证失败！')
  }
})()
```

---

**总结**：按照这个测试指南，管理员应该能够完全正常地显示和操作管理页面的所有功能。如果遇到问题，请按照排查步骤逐一检查。
