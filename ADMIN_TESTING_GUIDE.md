# 管理员功能测试指南

## 🎯 测试目标

验证管理员用户能够正常看到和访问管理平台的所有功能。

## 🔧 已完成的修复

### 1. 统一角色值
- ✅ 将所有角色值统一为小写 `'admin'` 和 `'user'`
- ✅ 更新 Vuex getters 使用正确的角色值
- ✅ 添加 `currentUser` getter 支持

### 2. Mock 数据系统
- ✅ 创建完整的 Mock 用户数据
- ✅ 实现 Mock 登录功能
- ✅ 添加快速登录命令

### 3. 登录系统优化
- ✅ 开发环境使用 Mock 数据
- ✅ 添加快速登录按钮
- ✅ 支持预设账号登录

### 4. 权限检查修复
- ✅ 更新路由守卫逻辑
- ✅ 修复组件权限判断
- ✅ 优化用户信息获取

## 🚀 测试步骤

### 步骤 1：清理浏览器状态
```javascript
// 在浏览器控制台执行
localStorage.clear()
sessionStorage.clear()
location.reload()
```

### 步骤 2：访问登录页面
访问：`http://localhost:3000/login`

### 步骤 3：使用快速登录（推荐）
在登录页面底部，点击红色的"管理员"按钮

### 步骤 4：验证登录状态
登录成功后，在控制台执行：
```javascript
// 检查用户信息
console.log('用户信息:', this.$store.getters['auth/currentUser'])
console.log('是否管理员:', this.$store.getters['auth/isAdmin'])
console.log('用户角色:', this.$store.getters['auth/userRole'])
```

### 步骤 5：检查管理员入口
在控制台页面应该能看到：

**✅ 页面顶部**：
- 黄色的"管理控制台"按钮

**✅ 管理员提示卡片**：
- 红色边框的权限提示卡片
- "系统管理员权限"标题
- "进入管理控制台"按钮

**✅ 快速操作区域**：
- 红色的"系统管理控制台"按钮
- 包含功能描述文字

**✅ 个人信息卡片**：
- 角色显示为"系统管理员"（红色徽章）
- "管理控制台"按钮

**✅ 导航栏**：
- 用户头像下拉菜单中的"管理控制台"选项

### 步骤 6：访问管理平台
点击任意一个"管理控制台"入口，应该：
- ✅ 成功跳转到 `/admin` 路径
- ✅ 看到管理平台界面
- ✅ 侧边栏显示各种管理功能

### 步骤 7：测试管理功能
在管理平台中测试：
- ✅ 数据概览页面
- ✅ 用户管理功能
- ✅ 题目管理功能
- ✅ 题集管理功能
- ✅ 系统设置功能

## 🔄 替代测试方法

### 方法 1：预设账号登录
**管理员账号**：
- 邮箱：`<EMAIL>`
- 密码：`admin123`

**普通用户账号**：
- 邮箱：`<EMAIL>`
- 密码：`user123`

### 方法 2：控制台命令
在浏览器控制台执行：
```javascript
// 快速登录为管理员
window.quickLoginAsAdmin()

// 快速登录为普通用户
window.quickLoginAsUser()
```

### 方法 3：手动设置用户状态
如果登录有问题，可以手动设置：
```javascript
// 手动设置管理员用户
const adminUser = {
  id: 1,
  username: 'admin',
  email: '<EMAIL>',
  nickname: '系统管理员',
  role: 'admin',
  avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=admin'
}

// 设置到 store
this.$store.commit('auth/SET_USER', adminUser)
this.$store.commit('auth/SET_TOKEN', 'mock-admin-token')
localStorage.setItem('token', 'mock-admin-token')

// 刷新页面
location.reload()
```

## 🐛 故障排除

### 问题 1：快速登录按钮不显示
**原因**：可能不是开发环境
**解决**：
```javascript
// 检查环境
console.log('当前环境:', process.env.NODE_ENV)

// 如果不是 development，手动显示按钮
// 在 Login.vue 中临时修改 isDevelopment 为 true
```

### 问题 2：登录后看不到管理员入口
**检查步骤**：
```javascript
// 1. 检查用户信息
const user = this.$store.getters['auth/currentUser']
console.log('用户信息:', user)

// 2. 检查角色值
console.log('用户角色:', user?.role)
console.log('角色类型:', typeof user?.role)

// 3. 检查权限判断
console.log('是否管理员:', user?.role === 'admin')
```

**解决方案**：
```javascript
// 如果角色值不正确，手动修正
if (user && user.role !== 'admin') {
  user.role = 'admin'
  this.$store.commit('auth/SET_USER', user)
}
```

### 问题 3：无法访问 /admin 路径
**检查步骤**：
```javascript
// 检查路由守卫条件
const isAuthenticated = this.$store.getters['auth/isAuthenticated']
const currentUser = this.$store.getters['auth/currentUser']

console.log('认证状态:', isAuthenticated)
console.log('当前用户:', currentUser)
console.log('管理员检查:', currentUser?.role === 'admin')
```

**解决方案**：
```javascript
// 确保所有条件都满足
if (!isAuthenticated) {
  // 重新登录
  this.$router.push('/login')
} else if (!currentUser || currentUser.role !== 'admin') {
  // 修正用户信息
  const adminUser = { /* 管理员用户信息 */ }
  this.$store.commit('auth/SET_USER', adminUser)
}
```

### 问题 4：页面刷新后丢失状态
**原因**：没有从 localStorage 恢复状态
**解决**：
```javascript
// 检查 localStorage
console.log('Token:', localStorage.getItem('token'))

// 手动恢复状态
const token = localStorage.getItem('token')
if (token) {
  this.$store.commit('auth/SET_TOKEN', token)
  // 设置用户信息
  const adminUser = { /* 管理员用户信息 */ }
  this.$store.commit('auth/SET_USER', adminUser)
}
```

## ✅ 预期结果

### 管理员用户应该能够：
1. ✅ 成功登录（快速登录或账号密码）
2. ✅ 在控制台看到 5 个管理入口
3. ✅ 成功访问 `/admin` 路径
4. ✅ 看到完整的管理平台界面
5. ✅ 使用所有管理功能模块

### 普通用户应该：
1. ✅ 正常登录和使用
2. ✅ 看不到任何管理入口
3. ✅ 无法访问 `/admin` 路径
4. ✅ 被重定向到首页

## 📋 完整检查清单

### 登录功能
- [ ] 快速登录按钮显示
- [ ] 管理员快速登录成功
- [ ] 普通用户快速登录成功
- [ ] 预设账号登录成功
- [ ] 控制台命令有效

### 权限显示
- [ ] 管理员看到所有入口
- [ ] 普通用户看不到入口
- [ ] 角色标识正确显示
- [ ] 权限提示卡片显示

### 路由访问
- [ ] 管理员能访问 /admin
- [ ] 普通用户被拒绝访问
- [ ] 路由守卫工作正常
- [ ] 重定向逻辑正确

### 管理功能
- [ ] 管理平台加载正常
- [ ] 侧边栏导航工作
- [ ] 各模块切换正常
- [ ] Mock 数据显示正常

### 状态持久化
- [ ] 刷新页面状态保持
- [ ] localStorage 正确存储
- [ ] 自动恢复登录状态
- [ ] Token 验证正常

## 🎯 成功标准

测试成功的标准是：
1. 管理员用户能够通过任意方式登录
2. 登录后能在控制台看到所有管理入口
3. 点击任意管理入口能成功进入管理平台
4. 管理平台的所有功能模块都能正常使用
5. 普通用户无法看到或访问管理功能

如果以上所有条件都满足，说明管理员功能已经正常工作！

---

**注意**：如果测试过程中遇到问题，请按照故障排除步骤逐一检查，大部分问题都可以通过控制台命令快速解决。
