#!/bin/bash

# Vue3 + TypeScript 转换脚本
# 帮助将剩余的 JavaScript 文件转换为 TypeScript

echo "🔄 开始 Vue3 + TypeScript 转换..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 转换结果
CONVERTED_FILES=0
SKIPPED_FILES=0
ERROR_FILES=0

# 转换函数
convert_js_to_ts() {
    local js_file="$1"
    local ts_file="${js_file%.js}.ts"
    
    echo -e "\n📋 转换: ${BLUE}$js_file${NC} → ${GREEN}$ts_file${NC}"
    
    if [ -f "$ts_file" ]; then
        echo -e "⚠️ ${YELLOW}目标文件已存在，跳过转换${NC}"
        SKIPPED_FILES=$((SKIPPED_FILES + 1))
        return 0
    fi
    
    if [ ! -f "$js_file" ]; then
        echo -e "❌ ${RED}源文件不存在${NC}"
        ERROR_FILES=$((ERROR_FILES + 1))
        return 1
    fi
    
    # 复制文件并重命名
    cp "$js_file" "$ts_file"
    
    if [ $? -eq 0 ]; then
        echo -e "✅ ${GREEN}文件转换成功${NC}"
        CONVERTED_FILES=$((CONVERTED_FILES + 1))
        
        # 删除原始 JS 文件
        rm "$js_file"
        echo -e "🗑️ ${YELLOW}已删除原始 JS 文件${NC}"
        
        return 0
    else
        echo -e "❌ ${RED}文件转换失败${NC}"
        ERROR_FILES=$((ERROR_FILES + 1))
        return 1
    fi
}

# 检查并转换 Store 模块
echo -e "\n${YELLOW}=== 1. 转换 Store 模块 ===${NC}"

store_modules=(
    "frontend/src/store/modules/problemset.js"
    "frontend/src/store/modules/problem.js"
    "frontend/src/store/modules/user.js"
    "frontend/src/store/modules/stats.js"
)

for module in "${store_modules[@]}"; do
    if [ -f "$module" ]; then
        convert_js_to_ts "$module"
    else
        echo -e "⚠️ ${YELLOW}文件不存在: $module${NC}"
    fi
done

# 检查并转换工具文件
echo -e "\n${YELLOW}=== 2. 转换工具文件 ===${NC}"

util_files=(
    "frontend/src/utils/constants.js"
    "frontend/src/utils/format.js"
    "frontend/src/utils/validation.js"
    "frontend/src/utils/storage.js"
)

for util_file in "${util_files[@]}"; do
    if [ -f "$util_file" ]; then
        convert_js_to_ts "$util_file"
    else
        echo -e "⚠️ ${YELLOW}文件不存在: $util_file${NC}"
    fi
done

# 检查 Vue 组件中的 JS 脚本
echo -e "\n${YELLOW}=== 3. 检查 Vue 组件 ===${NC}"

vue_files_with_js=0
vue_files_total=0

if [ -d "frontend/src" ]; then
    while IFS= read -r -d '' vue_file; do
        vue_files_total=$((vue_files_total + 1))
        
        # 检查是否使用了 <script> 而不是 <script setup lang="ts">
        if grep -q "<script>" "$vue_file" && ! grep -q "<script setup lang=\"ts\">" "$vue_file"; then
            echo -e "📋 需要转换: ${BLUE}$vue_file${NC}"
            vue_files_with_js=$((vue_files_with_js + 1))
        fi
    done < <(find frontend/src -name "*.vue" -print0)
fi

echo -e "📊 Vue 组件统计: 总计 $vue_files_total 个，需要转换 $vue_files_with_js 个"

# 安装 TypeScript 依赖
echo -e "\n${YELLOW}=== 4. 检查 TypeScript 依赖 ===${NC}"

cd frontend

if [ -f "package.json" ]; then
    # 检查是否已安装 TypeScript
    if npm list typescript > /dev/null 2>&1; then
        echo -e "✅ ${GREEN}TypeScript 已安装${NC}"
    else
        echo -e "📦 ${BLUE}安装 TypeScript 依赖...${NC}"
        npm install --save-dev typescript vue-tsc @types/node @types/nprogress @vue/eslint-config-typescript @typescript-eslint/eslint-plugin @typescript-eslint/parser
        
        if [ $? -eq 0 ]; then
            echo -e "✅ ${GREEN}TypeScript 依赖安装成功${NC}"
        else
            echo -e "❌ ${RED}TypeScript 依赖安装失败${NC}"
            ERROR_FILES=$((ERROR_FILES + 1))
        fi
    fi
else
    echo -e "❌ ${RED}package.json 不存在${NC}"
    ERROR_FILES=$((ERROR_FILES + 1))
fi

cd ..

# 验证 TypeScript 配置
echo -e "\n${YELLOW}=== 5. 验证 TypeScript 配置 ===${NC}"

config_files=(
    "frontend/tsconfig.json"
    "frontend/tsconfig.node.json"
    "frontend/vite.config.ts"
)

config_ok=true
for config_file in "${config_files[@]}"; do
    if [ -f "$config_file" ]; then
        echo -e "✅ ${GREEN}$config_file 存在${NC}"
    else
        echo -e "❌ ${RED}$config_file 不存在${NC}"
        config_ok=false
    fi
done

if [ "$config_ok" = true ]; then
    echo -e "✅ ${GREEN}TypeScript 配置完整${NC}"
else
    echo -e "⚠️ ${YELLOW}TypeScript 配置不完整${NC}"
fi

# 运行类型检查
echo -e "\n${YELLOW}=== 6. 运行类型检查 ===${NC}"

cd frontend

if [ -f "tsconfig.json" ] && command -v npx > /dev/null; then
    echo -e "🔍 ${BLUE}运行 TypeScript 类型检查...${NC}"
    
    npx vue-tsc --noEmit > /dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        echo -e "✅ ${GREEN}类型检查通过${NC}"
    else
        echo -e "⚠️ ${YELLOW}类型检查发现问题，请查看详细输出${NC}"
        echo -e "💡 ${BLUE}运行 'npm run type-check' 查看详细错误信息${NC}"
    fi
else
    echo -e "⚠️ ${YELLOW}无法运行类型检查（缺少配置文件或 npx）${NC}"
fi

cd ..

# 创建转换建议
echo -e "\n${YELLOW}=== 7. 转换建议 ===${NC}"

echo -e "📋 ${BLUE}Vue 组件转换建议：${NC}"
echo "1. 将 <script> 改为 <script setup lang=\"ts\">"
echo "2. 使用 Composition API 替代 Options API"
echo "3. 添加 Props 和 Emits 类型定义"
echo "4. 为 ref 和 reactive 添加类型注解"

echo -e "\n📋 ${BLUE}Store 模块转换建议：${NC}"
echo "1. 定义 State 接口"
echo "2. 为 mutations 和 actions 添加类型注解"
echo "3. 使用 Module<State, RootState> 类型"

echo -e "\n📋 ${BLUE}工具函数转换建议：${NC}"
echo "1. 为函数参数和返回值添加类型注解"
echo "2. 定义相关的接口和类型"
echo "3. 使用泛型提高代码复用性"

# 生成转换报告
echo -e "\n${BLUE}===========================================${NC}"
echo -e "${BLUE}           转换结果报告${NC}"
echo -e "${BLUE}===========================================${NC}"
echo -e "✅ ${GREEN}成功转换: $CONVERTED_FILES 个文件${NC}"
echo -e "⚠️ ${YELLOW}跳过转换: $SKIPPED_FILES 个文件${NC}"
echo -e "❌ ${RED}转换失败: $ERROR_FILES 个文件${NC}"
echo -e "📊 ${BLUE}Vue 组件: $vue_files_with_js/$vue_files_total 个需要手动转换${NC}"

# 下一步建议
echo -e "\n${BLUE}下一步操作建议：${NC}"

if [ $vue_files_with_js -gt 0 ]; then
    echo "1. 手动转换 Vue 组件到 Composition API + TypeScript"
    echo "   - 参考 QuickAddButton.vue 的转换示例"
    echo "   - 使用 <script setup lang=\"ts\">"
    echo "   - 添加类型定义"
fi

if [ $CONVERTED_FILES -gt 0 ]; then
    echo "2. 为转换后的 TypeScript 文件添加类型注解"
    echo "   - 函数参数和返回值类型"
    echo "   - 变量类型注解"
    echo "   - 接口定义"
fi

echo "3. 运行类型检查和测试"
echo "   cd frontend && npm run type-check"
echo "   cd frontend && npm run dev"

echo "4. 逐步完善类型定义"
echo "   - 添加更详细的接口定义"
echo "   - 使用泛型提高代码复用性"
echo "   - 添加 JSDoc 注释"

if [ $ERROR_FILES -eq 0 ] && [ $vue_files_with_js -eq 0 ]; then
    echo -e "\n🎉 ${GREEN}恭喜！基础转换已完成，项目已支持 TypeScript！${NC}"
    echo -e "🚀 ${BLUE}现在可以享受 TypeScript 带来的类型安全和开发体验提升！${NC}"
else
    echo -e "\n💡 ${YELLOW}转换未完全完成，请根据上述建议继续完善。${NC}"
fi

exit 0
