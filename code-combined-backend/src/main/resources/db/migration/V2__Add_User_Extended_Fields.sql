-- 添加用户扩展字段
-- 版本: V2
-- 描述: 为用户表添加积分、个人信息等扩展字段

-- 添加用户积分字段
ALTER TABLE `user` ADD COLUMN `points` INT DEFAULT 100 COMMENT '用户积分' AFTER `email_verified`;

-- 添加个人简介字段
ALTER TABLE `user` ADD COLUMN `bio` VARCHAR(500) DEFAULT NULL COMMENT '个人简介' AFTER `points`;

-- 添加所在地字段
ALTER TABLE `user` ADD COLUMN `location` VARCHAR(100) DEFAULT NULL COMMENT '所在地' AFTER `bio`;

-- 添加公司字段
ALTER TABLE `user` ADD COLUMN `company` VARCHAR(100) DEFAULT NULL COMMENT '公司' AFTER `location`;

-- 添加GitHub链接字段
ALTER TABLE `user` ADD COLUMN `github` VARCHAR(200) DEFAULT NULL COMMENT 'GitHub链接' AFTER `company`;

-- 添加个人网站字段
ALTER TABLE `user` ADD COLUMN `website` VARCHAR(200) DEFAULT NULL COMMENT '个人网站' AFTER `github`;

-- 为现有用户设置默认积分
UPDATE `user` SET `points` = 100 WHERE `points` IS NULL;

-- 添加索引
CREATE INDEX `idx_user_points` ON `user` (`points`);
CREATE INDEX `idx_user_location` ON `user` (`location`);
CREATE INDEX `idx_user_company` ON `user` (`company`);
