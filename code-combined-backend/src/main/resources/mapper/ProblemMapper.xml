<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.codecombined.mapper.ProblemMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.codecombined.entity.Problem">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="title" property="title" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="LONGVARCHAR"/>
        <result column="input_format" property="inputFormat" jdbcType="LONGVARCHAR"/>
        <result column="output_format" property="outputFormat" jdbcType="LONGVARCHAR"/>
        <result column="sample_input" property="sampleInput" jdbcType="LONGVARCHAR"/>
        <result column="sample_output" property="sampleOutput" jdbcType="LONGVARCHAR"/>
        <result column="hint" property="hint" jdbcType="LONGVARCHAR"/>
        <result column="difficulty" property="difficulty" jdbcType="VARCHAR"/>
        <result column="tags" property="tags" jdbcType="VARCHAR"/>
        <result column="time_limit" property="timeLimit" jdbcType="INTEGER"/>
        <result column="memory_limit" property="memoryLimit" jdbcType="INTEGER"/>
        <result column="creator_id" property="creatorId" jdbcType="BIGINT"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="view_count" property="viewCount" jdbcType="INTEGER"/>
        <result column="like_count" property="likeCount" jdbcType="INTEGER"/>
        <result column="submission_count" property="submissionCount" jdbcType="INTEGER"/>
        <result column="accepted_count" property="acceptedCount" jdbcType="INTEGER"/>
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
        <result column="updated_time" property="updatedTime" jdbcType="TIMESTAMP"/>
        <result column="deleted" property="deleted" jdbcType="BOOLEAN"/>
    </resultMap>

    <!-- 带创建者信息的结果映射 -->
    <resultMap id="ProblemWithCreatorMap" type="com.codecombined.entity.Problem" extends="BaseResultMap">
        <result column="creator_name" property="creatorName" jdbcType="VARCHAR"/>
        <result column="creator_nickname" property="creatorNickname" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础列名 -->
    <sql id="Base_Column_List">
        id, title, description, input_format, output_format, sample_input, sample_output,
        hint, difficulty, tags, time_limit, memory_limit, creator_id, status, view_count,
        like_count, submission_count, accepted_count, created_time, updated_time, deleted
    </sql>

    <!-- 带创建者信息的列名 -->
    <sql id="Problem_With_Creator_Column_List">
        p.id, p.title, p.description, p.input_format, p.output_format, p.sample_input, 
        p.sample_output, p.hint, p.difficulty, p.tags, p.time_limit, p.memory_limit, 
        p.creator_id, p.status, p.view_count, p.like_count, p.submission_count, 
        p.accepted_count, p.created_time, p.updated_time, p.deleted,
        u.username as creator_name, u.nickname as creator_nickname
    </sql>

    <!-- 分页查询题目列表（带创建者信息） -->
    <select id="selectProblemPage" resultMap="ProblemWithCreatorMap">
        SELECT 
        <include refid="Problem_With_Creator_Column_List"/>
        FROM problems p 
        LEFT JOIN user u ON p.creator_id = u.id 
        WHERE 1=1 
        ${ew.customSqlSegment}
    </select>

    <!-- 根据关键词搜索题目 -->
    <select id="searchProblems" parameterType="java.lang.String" resultMap="ProblemWithCreatorMap">
        SELECT 
        <include refid="Problem_With_Creator_Column_List"/>
        FROM problems p 
        LEFT JOIN user u ON p.creator_id = u.id 
        WHERE p.status = 'PUBLISHED' 
        AND p.deleted = 0
        <if test="keyword != null and keyword != ''">
            AND (
                p.title LIKE CONCAT('%', #{keyword,jdbcType=VARCHAR}, '%') 
                OR p.description LIKE CONCAT('%', #{keyword,jdbcType=VARCHAR}, '%') 
                OR p.tags LIKE CONCAT('%', #{keyword,jdbcType=VARCHAR}, '%')
            )
        </if>
        ORDER BY p.created_time DESC
    </select>

    <!-- 根据难度查询题目 -->
    <select id="findByDifficulty" parameterType="java.lang.String" resultMap="ProblemWithCreatorMap">
        SELECT 
        <include refid="Problem_With_Creator_Column_List"/>
        FROM problems p 
        LEFT JOIN user u ON p.creator_id = u.id 
        WHERE p.status = 'PUBLISHED' 
        AND p.difficulty = #{difficulty,jdbcType=VARCHAR}
        AND p.deleted = 0
        ORDER BY p.created_time DESC
    </select>

    <!-- 根据标签查询题目 -->
    <select id="findByTag" parameterType="java.lang.String" resultMap="ProblemWithCreatorMap">
        SELECT 
        <include refid="Problem_With_Creator_Column_List"/>
        FROM problems p 
        LEFT JOIN user u ON p.creator_id = u.id 
        WHERE p.status = 'PUBLISHED' 
        AND p.tags LIKE CONCAT('%', #{tag,jdbcType=VARCHAR}, '%')
        AND p.deleted = 0
        ORDER BY p.created_time DESC
    </select>

    <!-- 根据创建者查询题目 -->
    <select id="findByCreator" parameterType="java.lang.Long" resultMap="ProblemWithCreatorMap">
        SELECT 
        <include refid="Problem_With_Creator_Column_List"/>
        FROM problems p 
        LEFT JOIN user u ON p.creator_id = u.id 
        WHERE p.creator_id = #{creatorId,jdbcType=BIGINT}
        AND p.deleted = 0
        ORDER BY p.created_time DESC
    </select>

    <!-- 获取热门题目（按浏览量排序） -->
    <select id="findHotProblems" parameterType="java.lang.Integer" resultMap="ProblemWithCreatorMap">
        SELECT 
        <include refid="Problem_With_Creator_Column_List"/>
        FROM problems p 
        LEFT JOIN user u ON p.creator_id = u.id 
        WHERE p.status = 'PUBLISHED'
        AND p.deleted = 0
        ORDER BY p.view_count DESC, p.like_count DESC 
        LIMIT #{limit,jdbcType=INTEGER}
    </select>

    <!-- 获取最新题目 -->
    <select id="findLatestProblems" parameterType="java.lang.Integer" resultMap="ProblemWithCreatorMap">
        SELECT 
        <include refid="Problem_With_Creator_Column_List"/>
        FROM problems p 
        LEFT JOIN user u ON p.creator_id = u.id 
        WHERE p.status = 'PUBLISHED'
        AND p.deleted = 0
        ORDER BY p.created_time DESC 
        LIMIT #{limit,jdbcType=INTEGER}
    </select>

    <!-- 增加浏览次数 -->
    <update id="incrementViewCount" parameterType="java.lang.Long">
        UPDATE problems 
        SET view_count = view_count + 1,
            updated_time = NOW()
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 增加点赞次数 -->
    <update id="incrementLikeCount" parameterType="java.lang.Long">
        UPDATE problems 
        SET like_count = like_count + 1,
            updated_time = NOW()
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 减少点赞次数 -->
    <update id="decrementLikeCount" parameterType="java.lang.Long">
        UPDATE problems 
        SET like_count = like_count - 1,
            updated_time = NOW()
        WHERE id = #{id,jdbcType=BIGINT} 
        AND like_count > 0
    </update>

    <!-- 增加提交次数 -->
    <update id="incrementSubmissionCount" parameterType="java.lang.Long">
        UPDATE problems 
        SET submission_count = submission_count + 1,
            updated_time = NOW()
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 增加通过次数 -->
    <update id="incrementAcceptedCount" parameterType="java.lang.Long">
        UPDATE problems 
        SET accepted_count = accepted_count + 1,
            updated_time = NOW()
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 获取题目统计信息 -->
    <select id="getStatistics" resultType="java.util.Map">
        SELECT 
            COUNT(*) as total,
            SUM(CASE WHEN difficulty = 'EASY' THEN 1 ELSE 0 END) as easy_count,
            SUM(CASE WHEN difficulty = 'MEDIUM' THEN 1 ELSE 0 END) as medium_count,
            SUM(CASE WHEN difficulty = 'HARD' THEN 1 ELSE 0 END) as hard_count,
            SUM(view_count) as total_views,
            SUM(like_count) as total_likes,
            SUM(submission_count) as total_submissions,
            SUM(accepted_count) as total_accepted
        FROM problems 
        WHERE status = 'PUBLISHED'
        AND deleted = 0
    </select>

    <!-- 根据多个条件查询题目 -->
    <select id="findProblemsWithConditions" resultMap="ProblemWithCreatorMap">
        SELECT 
        <include refid="Problem_With_Creator_Column_List"/>
        FROM problems p 
        LEFT JOIN user u ON p.creator_id = u.id 
        WHERE p.deleted = 0
        <if test="status != null and status != ''">
            AND p.status = #{status,jdbcType=VARCHAR}
        </if>
        <if test="difficulty != null and difficulty != ''">
            AND p.difficulty = #{difficulty,jdbcType=VARCHAR}
        </if>
        <if test="creatorId != null">
            AND p.creator_id = #{creatorId,jdbcType=BIGINT}
        </if>
        <if test="keyword != null and keyword != ''">
            AND (
                p.title LIKE CONCAT('%', #{keyword,jdbcType=VARCHAR}, '%') 
                OR p.description LIKE CONCAT('%', #{keyword,jdbcType=VARCHAR}, '%')
                OR p.tags LIKE CONCAT('%', #{keyword,jdbcType=VARCHAR}, '%')
            )
        </if>
        ORDER BY p.created_time DESC
    </select>

    <!-- 批量更新题目状态 -->
    <update id="batchUpdateStatus">
        UPDATE problems 
        SET status = #{status,jdbcType=VARCHAR},
            updated_time = NOW()
        WHERE id IN
        <foreach collection="problemIds" item="problemId" open="(" separator="," close=")">
            #{problemId,jdbcType=BIGINT}
        </foreach>
    </update>

</mapper>
