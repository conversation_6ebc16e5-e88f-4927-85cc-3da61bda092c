server:
  port: 8080

spring:
  
  # 数据库配置
  datasource:
    url: ********************************************************************************************************************************************************************
    username: root
    password: 123456
#    hikari:
#
#      driver-class-name: com.mysql.cj.jdbc.Driver
#      jdbc-url: *********************************************************************************************************************************************************************
#      username: root
#      password: 123456


  # Redis配置
  data:
    redis:
      host: ***********
      port: 6379
      password:
      database: 0
      timeout: 3000ms
      lettuce:
        pool:
          max-active: 8
          max-wait: -1ms
          max-idle: 8
          min-idle: 0

  # 邮件配置（开发环境）
  mail:
    host: smtp.qq.com
    port: 587
    username: <EMAIL>
    password: your-email-password
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath*:/mapper/**/*.xml

# JWT配置
jwt:
  secret: i7h3nXpL9kJmZqW0sTrXd9lVbEpOcRtAuFvQwGsLi2pT8rY3sKpF0eNmBqW7tLvXcHs=
  expiration: 86400000  # 24小时
  header: Authorization
  prefix: Bearer


# 日志配置
logging:
  level:
    com.codecombined: debug
    org.springframework.security: debug
    org.springframework.web: debug
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/code-combined-dev.log

# 开发环境特殊配置
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always
