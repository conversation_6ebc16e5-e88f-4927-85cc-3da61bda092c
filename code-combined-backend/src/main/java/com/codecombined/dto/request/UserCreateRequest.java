package com.codecombined.dto.request;

import com.codecombined.enums.UserRole;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;


/**
 * 管理员创建用户请求DTO
 */
@Data
@Schema(description = "管理员创建用户请求")
public class UserCreateRequest {

    @Schema(description = "用户名", example = "testuser")
    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 20, message = "用户名长度必须在3-20个字符之间")
    private String username;

    @Schema(description = "邮箱", example = "<EMAIL>")
    @NotBlank(message = "邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    private String email;

    @Schema(description = "密码", example = "123456")
    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 20, message = "密码长度必须在6-20个字符之间")
    private String password;

    @Schema(description = "昵称", example = "测试用户")
    @Size(max = 50, message = "昵称长度不能超过50个字符")
    private String nickname;

    @Schema(description = "用户角色", example = "USER")
    @NotNull(message = "用户角色不能为空")
    private UserRole role;

    @Schema(description = "用户状态：1-启用，0-禁用", example = "1")
    @NotNull(message = "用户状态不能为空")
    private Integer status;

    @Schema(description = "头像URL", example = "https://example.com/avatar.jpg")
    private String avatar;

    @Schema(description = "个人简介", example = "这是一个测试用户")
    @Size(max = 500, message = "个人简介长度不能超过500个字符")
    private String bio;

    @Schema(description = "所在地", example = "北京市")
    @Size(max = 100, message = "所在地长度不能超过100个字符")
    private String location;

    @Schema(description = "公司", example = "某科技公司")
    @Size(max = 100, message = "公司名称长度不能超过100个字符")
    private String company;

    @Schema(description = "GitHub链接", example = "https://github.com/username")
    @Size(max = 200, message = "GitHub链接长度不能超过200个字符")
    private String github;

    @Schema(description = "个人网站", example = "https://example.com")
    @Size(max = 200, message = "个人网站链接长度不能超过200个字符")
    private String website;

    @Schema(description = "初始积分", example = "100")
    private Integer points;

    @Schema(description = "是否需要邮箱验证", example = "false")
    private Boolean requireEmailVerification;
}
