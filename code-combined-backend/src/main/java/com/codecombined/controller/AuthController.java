package com.codecombined.controller;

import com.codecombined.common.Result;
import com.codecombined.dto.LoginRequest;
import com.codecombined.dto.RegisterRequest;
import com.codecombined.service.UserService;
import com.codecombined.util.IpUtil;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.constraints.Email;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


import java.util.HashMap;
import java.util.Map;

/**
 * 认证控制器
 * 
 * <AUTHOR> Team
 * @since 2025-06-22
 */
@Slf4j
@RestController
@RequestMapping("/api/auth")
@Tag(name = "认证管理", description = "用户认证相关接口")
public class AuthController {

    @Autowired
    private UserService userService;

    /**
     * 用户注册
     */
    @PostMapping("/register")
    @Operation(summary = "用户注册")
    public Result<String> register(@Validated @RequestBody RegisterRequest request) {
        try {
            userService.register(request);
            return Result.success("注册成功");
        } catch (Exception e) {
            log.error("用户注册失败: {}", e.getMessage());
            return Result.error(e.getMessage());
        }
    }

    /**
     * 用户登录
     */
    @PostMapping("/login")
    @Operation(summary = "用户登录")
    public Result<Map<String, Object>> login(@Validated @RequestBody LoginRequest request,
                                            HttpServletRequest httpRequest) {
        try {
            String token = userService.login(request);
            
            // 更新最后登录信息
            String ip = IpUtil.getClientIp(httpRequest);
            // 这里需要从token中获取用户ID，暂时先不更新
            
            Map<String, Object> data = new HashMap<>();
            data.put("token", token);
            data.put("tokenType", "Bearer");
            
            return Result.success("登录成功", data);
        } catch (Exception e) {
            log.error("用户登录失败: {}", e.getMessage());
            return Result.error(e.getMessage());
        }
    }

    /**
     * 发送邮箱验证码
     */
    @PostMapping("/send-email-code")
    @Operation(summary = "发送邮箱验证码")
    public Result<String> sendEmailCode(@RequestParam @Email(message = "邮箱格式不正确") String email) {
        try {
            userService.sendEmailCode(email);
            return Result.success("验证码已发送，请查收邮件");
        } catch (Exception e) {
            log.error("发送邮箱验证码失败: {}", e.getMessage());
            return Result.error(e.getMessage());
        }
    }

    /**
     * 检查用户名是否可用
     */
    @GetMapping("/check-username")
    @Operation(summary = "检查用户名是否可用")
    public Result<Map<String, Boolean>> checkUsername(@RequestParam String username) {
        boolean available = userService.findByUsername(username) == null;
        Map<String, Boolean> data = new HashMap<>();
        data.put("available", available);
        return Result.success(data);
    }

    /**
     * 检查邮箱是否可用
     */
    @GetMapping("/check-email")
    @Operation(summary = "检查邮箱是否可用")
    public Result<Map<String, Boolean>> checkEmail(@RequestParam @Email(message = "邮箱格式不正确") String email) {
        boolean available = userService.findByEmail(email) == null;
        Map<String, Boolean> data = new HashMap<>();
        data.put("available", available);
        return Result.success(data);
    }
}
