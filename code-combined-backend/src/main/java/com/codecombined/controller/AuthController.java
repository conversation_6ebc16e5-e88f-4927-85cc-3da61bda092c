package com.codecombined.controller;

import com.codecombined.common.Result;
import com.codecombined.dto.LoginRequest;
import com.codecombined.dto.RegisterRequest;
import com.codecombined.entity.User;
import com.codecombined.security.UserPrincipal;
import com.codecombined.service.UserService;
import com.codecombined.util.IpUtil;
import com.codecombined.util.JwtUtils;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.constraints.Email;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


import java.util.HashMap;
import java.util.Map;

/**
 * 认证控制器
 * 
 * <AUTHOR> Team
 * @since 2025-06-22
 */
@Slf4j
@RestController
@RequestMapping("/api/auth")
@Tag(name = "认证管理", description = "用户认证相关接口")
public class AuthController {

    @Autowired
    private UserService userService;

    @Autowired
    private JwtUtils jwtUtils;

    /**
     * 用户注册
     */
    @PostMapping("/register")
    @Operation(summary = "用户注册")
    public Result<String> register(@Validated @RequestBody RegisterRequest request) {
        try {
            userService.register(request);
            return Result.success("注册成功");
        } catch (Exception e) {
            log.error("用户注册失败: {}", e.getMessage());
            return Result.error(e.getMessage());
        }
    }

    /**
     * 用户登录
     */
    @PostMapping("/login")
    @Operation(summary = "用户登录")
    public Result<Map<String, Object>> login(@Validated @RequestBody LoginRequest request,
                                            HttpServletRequest httpRequest) {
        try {
            String token = userService.login(request);
            
            // 更新最后登录信息
            String ip = IpUtil.getClientIp(httpRequest);
            // 这里需要从token中获取用户ID，暂时先不更新
            
            Map<String, Object> data = new HashMap<>();
            data.put("token", token);
            data.put("tokenType", "Bearer");
            
            return Result.success("登录成功", data);
        } catch (Exception e) {
            log.error("用户登录失败: {}", e.getMessage());
            return Result.error(e.getMessage());
        }
    }

    /**
     * 发送邮箱验证码
     */
    @PostMapping("/send-email-code")
    @Operation(summary = "发送邮箱验证码")
    public Result<String> sendEmailCode(@RequestParam @Email(message = "邮箱格式不正确") String email) {
        try {
            userService.sendEmailCode(email);
            return Result.success("验证码已发送，请查收邮件");
        } catch (Exception e) {
            log.error("发送邮箱验证码失败: {}", e.getMessage());
            return Result.error(e.getMessage());
        }
    }

    /**
     * 检查用户名是否可用
     */
    @GetMapping("/check-username")
    @Operation(summary = "检查用户名是否可用")
    public Result<Map<String, Boolean>> checkUsername(@RequestParam String username) {
        boolean available = userService.findByUsername(username) == null;
        Map<String, Boolean> data = new HashMap<>();
        data.put("available", available);
        return Result.success(data);
    }

    /**
     * 检查邮箱是否可用
     */
    @GetMapping("/check-email")
    @Operation(summary = "检查邮箱是否可用")
    public Result<Map<String, Boolean>> checkEmail(@RequestParam @Email(message = "邮箱格式不正确") String email) {
        boolean available = userService.findByEmail(email) == null;
        Map<String, Boolean> data = new HashMap<>();
        data.put("available", available);
        return Result.success(data);
    }

    /**
     * 获取当前用户信息
     */
    @GetMapping("/me")
    @Operation(summary = "获取当前用户信息")
    public Result<Map<String, Object>> getCurrentUser() {
        try {
            // 从Security上下文获取认证信息
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication == null || !authentication.isAuthenticated()) {
                return Result.error("用户未认证");
            }

            // 获取用户主体信息
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();

            // 从数据库获取完整用户信息
            User user = userService.getById(userPrincipal.getUserId());
            if (user == null) {
                return Result.error("用户不存在");
            }

            // 构建返回数据
            Map<String, Object> userData = new HashMap<>();
            userData.put("id", user.getId());
            userData.put("username", user.getUsername());
            userData.put("email", user.getEmail());
            userData.put("nickname", user.getNickname());
            userData.put("avatar", user.getAvatar());
            userData.put("role", user.getRole());
            userData.put("status", user.getStatus());
            userData.put("points", user.getPoints());
            userData.put("bio", user.getBio());
            userData.put("location", user.getLocation());
            userData.put("company", user.getCompany());
            userData.put("github", user.getGithub());
            userData.put("website", user.getWebsite());
            userData.put("createTime", user.getCreateTime());
            userData.put("lastLoginTime", user.getLastLoginTime());

            return Result.success("获取用户信息成功", userData);
        } catch (Exception e) {
            log.error("获取当前用户信息失败: {}", e.getMessage());
            return Result.error("获取用户信息失败");
        }
    }
}
