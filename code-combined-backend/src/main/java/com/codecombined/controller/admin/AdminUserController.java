package com.codecombined.controller.admin;

import com.codecombined.common.Result;
import com.codecombined.dto.request.UserCreateRequest;
import com.codecombined.dto.request.UserUpdateRequest;
import com.codecombined.dto.request.UserQueryRequest;
import com.codecombined.dto.response.UserDetailResponse;
import com.codecombined.dto.response.UserListResponse;
import com.codecombined.entity.User;
import com.codecombined.enums.UserRole;
import com.codecombined.service.UserService;
import com.codecombined.annotation.RequireRole;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


import java.util.List;

/**
 * 管理员用户管理控制器
 * 只有管理员才能访问的用户管理功能
 */
@Tag(name = "管理员用户管理", description = "管理员专用的用户管理接口")
@RestController
@RequestMapping("/api/admin/users")
@RequiredArgsConstructor
@Slf4j
@Validated
@RequireRole(UserRole.ADMIN)
public class AdminUserController {

    private final UserService userService;

    /**
     * 分页查询用户列表
     */
    @Operation(summary = "分页查询用户列表", description = "管理员查看所有用户，支持搜索和筛选")
    @GetMapping
    public Result<IPage<UserListResponse>> getUserList(
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") Integer current,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "搜索关键词") @RequestParam(required = false) String keyword,
            @Parameter(description = "用户角色") @RequestParam(required = false) UserRole role,
            @Parameter(description = "用户状态") @RequestParam(required = false) Integer status,
            @Parameter(description = "开始时间") @RequestParam(required = false) String startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) String endTime
    ) {
        log.info("管理员查询用户列表: current={}, size={}, keyword={}, role={}, status={}", 
                current, size, keyword, role, status);
        
        UserQueryRequest queryRequest = UserQueryRequest.builder()
                .keyword(keyword)
                .role(role)
                .status(status)
                .startTime(startTime)
                .endTime(endTime)
                .build();
        
        Page<User> page = new Page<>(current, size);
        IPage<UserListResponse> result = userService.getUserListForAdmin(page, queryRequest);
        
        return Result.success(result);
    }

    /**
     * 获取用户详细信息
     */
    @Operation(summary = "获取用户详细信息", description = "管理员查看用户的详细信息和统计数据")
    @GetMapping("/{userId}")
    public Result<UserDetailResponse> getUserDetail(
            @Parameter(description = "用户ID") @PathVariable @NotNull Long userId
    ) {
        log.info("管理员查看用户详情: userId={}", userId);
        
        UserDetailResponse userDetail = userService.getUserDetailForAdmin(userId);
        return Result.success(userDetail);
    }

    /**
     * 创建用户
     */
    @Operation(summary = "创建用户", description = "管理员创建新用户")
    @PostMapping
    public Result<Long> createUser(@Valid @RequestBody UserCreateRequest request) {
        log.info("管理员创建用户: username={}, email={}", request.getUsername(), request.getEmail());
        
        Long userId = userService.createUserByAdmin(request);
        return Result.success("用户创建成功",userId);
    }

    /**
     * 更新用户信息
     */
    @Operation(summary = "更新用户信息", description = "管理员更新用户信息")
    @PutMapping("/{userId}")
    public Result<String> updateUser(
            @Parameter(description = "用户ID") @PathVariable @NotNull Long userId,
            @Valid @RequestBody UserUpdateRequest request
    ) {
        log.info("管理员更新用户: userId={}, username={}", userId, request.getUsername());
        
        userService.updateUserByAdmin(userId, request);
        return Result.success("用户信息更新成功");
    }

    /**
     * 删除用户
     */
    @Operation(summary = "删除用户", description = "管理员删除用户（软删除）")
    @DeleteMapping("/{userId}")
    public Result<String> deleteUser(
            @Parameter(description = "用户ID") @PathVariable @NotNull Long userId
    ) {
        log.info("管理员删除用户: userId={}", userId);
        
        userService.deleteUserByAdmin(userId);
        return Result.success("用户删除成功");
    }

    /**
     * 批量删除用户
     */
    @Operation(summary = "批量删除用户", description = "管理员批量删除用户")
    @DeleteMapping("/batch")
    public Result<String> batchDeleteUsers(@RequestBody List<Long> userIds) {
        log.info("管理员批量删除用户: userIds={}", userIds);
        
        userService.batchDeleteUsersByAdmin(userIds);
        return Result.success("批量删除成功");
    }

    /**
     * 启用/禁用用户
     */
    @Operation(summary = "启用/禁用用户", description = "管理员启用或禁用用户账户")
    @PutMapping("/{userId}/status")
    public Result<String> toggleUserStatus(
            @Parameter(description = "用户ID") @PathVariable @NotNull Long userId,
            @Parameter(description = "状态：1-启用，0-禁用") @RequestParam @NotNull Integer status
    ) {
        log.info("管理员切换用户状态: userId={}, status={}", userId, status);
        
        userService.toggleUserStatusByAdmin(userId, status);
        String message = status == 1 ? "用户已启用" : "用户已禁用";
        return Result.success(message);
    }

    /**
     * 批量启用/禁用用户
     */
    @Operation(summary = "批量启用/禁用用户", description = "管理员批量启用或禁用用户")
    @PutMapping("/batch/status")
    public Result<String> batchToggleUserStatus(
            @RequestBody List<Long> userIds,
            @Parameter(description = "状态：1-启用，0-禁用") @RequestParam @NotNull Integer status
    ) {
        log.info("管理员批量切换用户状态: userIds={}, status={}", userIds, status);
        
        userService.batchToggleUserStatusByAdmin(userIds, status);
        String message = status == 1 ? "批量启用成功" : "批量禁用成功";
        return Result.success(message);
    }

    /**
     * 重置用户密码
     */
    @Operation(summary = "重置用户密码", description = "管理员重置用户密码")
    @PutMapping("/{userId}/password/reset")
    public Result<String> resetUserPassword(
            @Parameter(description = "用户ID") @PathVariable @NotNull Long userId
    ) {
        log.info("管理员重置用户密码: userId={}", userId);
        
        String newPassword = userService.resetUserPasswordByAdmin(userId);
        return Result.success(newPassword, "密码重置成功");
    }

    /**
     * 获取用户统计信息
     */
    @Operation(summary = "获取用户统计信息", description = "管理员查看用户相关的统计数据")
    @GetMapping("/statistics")
    public Result<Object> getUserStatistics() {
        log.info("管理员查看用户统计信息");
        
        Object statistics = userService.getUserStatisticsForAdmin();
        return Result.success(statistics);
    }

    /**
     * 导出用户数据
     */
    @Operation(summary = "导出用户数据", description = "管理员导出用户数据为Excel")
    @GetMapping("/export")
    public Result<String> exportUsers(
            @Parameter(description = "搜索关键词") @RequestParam(required = false) String keyword,
            @Parameter(description = "用户角色") @RequestParam(required = false) UserRole role,
            @Parameter(description = "用户状态") @RequestParam(required = false) Integer status
    ) {
        log.info("管理员导出用户数据: keyword={}, role={}, status={}", keyword, role, status);
        
        UserQueryRequest queryRequest = UserQueryRequest.builder()
                .keyword(keyword)
                .role(role)
                .status(status)
                .build();
        
        String downloadUrl = userService.exportUsersForAdmin(queryRequest);
        return Result.success(downloadUrl, "导出成功");
    }
}
