package com.codecombined.utils;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.util.StringUtils;



/**
 * IP工具类
 * 
 * <AUTHOR> Team
 * @since 2025-06-22
 */
public class IpUtil {

    private static final String UNKNOWN = "unknown";
    private static final String LOCALHOST_IPV4 = "127.0.0.1";
    private static final String LOCALHOST_IPV6 = "0:0:0:0:0:0:0:1";

    /**
     * 获取客户端真实IP地址
     */
    public static String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        
        if (!isValidIp(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        
        if (!isValidIp(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        
        if (!isValidIp(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        
        if (!isValidIp(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        
        if (!isValidIp(ip)) {
            ip = request.getHeader("X-Real-IP");
        }
        
        if (!isValidIp(ip)) {
            ip = request.getRemoteAddr();
        }

        // 处理多个IP的情况，取第一个有效IP
        if (StringUtils.hasText(ip) && ip.contains(",")) {
            ip = ip.split(",")[0].trim();
        }

        // 处理本地回环地址
        if (LOCALHOST_IPV6.equals(ip)) {
            ip = LOCALHOST_IPV4;
        }

        return ip;
    }

    /**
     * 验证IP是否有效
     */
    private static boolean isValidIp(String ip) {
        return StringUtils.hasText(ip) && !UNKNOWN.equalsIgnoreCase(ip);
    }

    /**
     * 判断是否为内网IP
     */
    public static boolean isInternalIp(String ip) {
        if (!StringUtils.hasText(ip)) {
            return false;
        }

        String[] parts = ip.split("\\.");
        if (parts.length != 4) {
            return false;
        }

        try {
            int first = Integer.parseInt(parts[0]);
            int second = Integer.parseInt(parts[1]);

            // 10.0.0.0 - **************
            if (first == 10) {
                return true;
            }

            // ********** - **************
            if (first == 172 && second >= 16 && second <= 31) {
                return true;
            }

            // *********** - ***************
            if (first == 192 && second == 168) {
                return true;
            }

            // ********* - ***************
            if (first == 127) {
                return true;
            }

            return false;
        } catch (NumberFormatException e) {
            return false;
        }
    }
}
