package com.codecombined.interceptor;

import com.codecombined.util.JwtUtil;
import com.codecombined.util.UserContext;
import com.codecombined.common.Result;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;


import java.io.IOException;

/**
 * JWT认证拦截器
 * 负责解析JWT Token并设置用户上下文
 */
//@Component
@RequiredArgsConstructor
@Slf4j
public class AuthInterceptor implements HandlerInterceptor {

    private final JwtUtil jwtUtil;
    private final ObjectMapper objectMapper;

    /**
     * Token请求头名称
     */
    private static final String AUTHORIZATION_HEADER = "Authorization";
    
    /**
     * Token前缀
     */
    private static final String BEARER_PREFIX = "Bearer ";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 获取请求路径
        String requestURI = request.getRequestURI();
        
        // 跳过不需要认证的路径
        if (isPublicPath(requestURI)) {
            log.debug("跳过公开路径的认证检查: {}", requestURI);
            return true;
        }

        // 从请求头中获取Token
        String token = extractTokenFromRequest(request);
        
        if (!StringUtils.hasText(token)) {
            log.warn("请求缺少Token: {}", requestURI);
            writeErrorResponse(response, "未提供认证Token", 401);
            return false;
        }

        // 验证Token
        if (!jwtUtil.validateToken(token)) {
            log.warn("Token验证失败: {}", requestURI);
            writeErrorResponse(response, "Token无效或已过期", 401);
            return false;
        }

        // 提取用户信息并设置到上下文
        try {
            UserContext.UserInfo userInfo = jwtUtil.extractUserInfo(token);
            if (userInfo == null) {
                log.warn("无法从Token中提取用户信息: {}", requestURI);
                writeErrorResponse(response, "Token中的用户信息无效", 401);
                return false;
            }

            // 设置额外的请求信息
            userInfo.setIpAddress(getClientIpAddress(request));
            userInfo.setUserAgent(request.getHeader("User-Agent"));

            // 设置用户上下文
            UserContext.setCurrentUser(userInfo);
            
            log.debug("用户认证成功: userId={}, username={}, role={}, ip={}", 
                    userInfo.getUserId(), userInfo.getUsername(), userInfo.getRole(), userInfo.getIpAddress());

            // 检查Token是否需要刷新
            if (jwtUtil.shouldRefreshToken(token)) {
                try {
                    String newToken = jwtUtil.refreshToken(token);
                    response.setHeader("New-Token", newToken);
                    log.debug("Token已刷新: userId={}", userInfo.getUserId());
                } catch (Exception e) {
                    log.warn("Token刷新失败: {}", e.getMessage());
                }
            }

            return true;
        } catch (Exception e) {
            log.error("处理用户认证时发生错误: {}", e.getMessage(), e);
            writeErrorResponse(response, "认证处理失败", 500);
            return false;
        }
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        // 清除用户上下文
        try {
            long duration = UserContext.getRequestDuration();
            if (duration > 0) {
                log.debug("请求完成: {} - 耗时: {}ms - 用户: {}", 
                        request.getRequestURI(), duration, UserContext.getContextString());
            }
        } catch (Exception e) {
            log.warn("记录请求完成信息时发生错误: {}", e.getMessage());
        } finally {
            UserContext.clear();
        }
    }

    /**
     * 从请求中提取Token
     * 
     * @param request HTTP请求
     * @return Token字符串，如果没有则返回null
     */
    private String extractTokenFromRequest(HttpServletRequest request) {
        // 从Authorization头中获取
        String authHeader = request.getHeader(AUTHORIZATION_HEADER);
        if (StringUtils.hasText(authHeader) && authHeader.startsWith(BEARER_PREFIX)) {
            return authHeader.substring(BEARER_PREFIX.length());
        }

        // 从请求参数中获取（用于某些特殊场景，如WebSocket）
        String tokenParam = request.getParameter("token");
        if (StringUtils.hasText(tokenParam)) {
            return tokenParam;
        }

        return null;
    }

    /**
     * 获取客户端真实IP地址
     * 
     * @param request HTTP请求
     * @return 客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String[] headerNames = {
            "X-Forwarded-For",
            "X-Real-IP",
            "Proxy-Client-IP",
            "WL-Proxy-Client-IP",
            "HTTP_CLIENT_IP",
            "HTTP_X_FORWARDED_FOR"
        };

        for (String headerName : headerNames) {
            String ip = request.getHeader(headerName);
            if (StringUtils.hasText(ip) && !"unknown".equalsIgnoreCase(ip)) {
                // 多级代理的情况，取第一个IP
                if (ip.contains(",")) {
                    ip = ip.split(",")[0].trim();
                }
                return ip;
            }
        }

        return request.getRemoteAddr();
    }

    /**
     * 检查是否是公开路径（不需要认证）
     * 
     * @param requestURI 请求URI
     * @return true表示是公开路径，false表示需要认证
     */
    private boolean isPublicPath(String requestURI) {
        // 公开路径列表
        String[] publicPaths = {
            "/api/auth/login",
            "/api/auth/register",
            "/api/auth/refresh",
            "/api/auth/logout",
            "/api/public/",
            "/swagger-ui/",
            "/v3/api-docs",
            "/swagger-resources/",
            "/webjars/",
            "/favicon.ico",
            "/error",
            "/actuator/health"
        };

        for (String publicPath : publicPaths) {
            if (requestURI.startsWith(publicPath)) {
                return true;
            }
        }

        // 静态资源
        if (requestURI.matches(".*\\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$")) {
            return true;
        }

        return false;
    }

    /**
     * 写入错误响应
     * 
     * @param response HTTP响应
     * @param message 错误消息
     * @param status HTTP状态码
     */
    private void writeErrorResponse(HttpServletResponse response, String message, int status) throws IOException {
        response.setStatus(status);
        response.setContentType("application/json;charset=UTF-8");
        
        Result<Object> result = Result.error(message);
        String jsonResponse = objectMapper.writeValueAsString(result);
        
        response.getWriter().write(jsonResponse);
    }
}
