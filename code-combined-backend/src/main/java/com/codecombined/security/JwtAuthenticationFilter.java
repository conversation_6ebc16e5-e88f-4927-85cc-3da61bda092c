package com.codecombined.security;

import com.codecombined.util.JwtUtils;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;


import java.io.IOException;
import java.util.Collections;

/**
 * JWT认证过滤器
 * 
 * <AUTHOR> Team
 * @since 2025-06-22
 */
@Slf4j
@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    @Autowired
    private JwtUtils jwtUtils;

    @Override
    protected void doFilterInternal(HttpServletRequest request, @NotNull HttpServletResponse response,
                                    @NotNull FilterChain filterChain) throws ServletException, IOException {
        
        String requestURI = request.getRequestURI();
        
        // 跳过不需要认证的路径
        if (shouldSkipAuthentication(requestURI)) {
            filterChain.doFilter(request, response);
            log.debug("跳过认证: {}", requestURI);
            return;
        }

        String authToken = jwtUtils.getTokenFromRequest(request);

        if (StringUtils.hasText(authToken)) {
            try {
                String username = jwtUtils.getUsernameFromToken(authToken);

                if (username != null && SecurityContextHolder.getContext().getAuthentication() == null) {
                    // 验证token
                    if (jwtUtils.validateToken(authToken)) {
                        // 获取用户信息
                        Long userId = jwtUtils.getUserIdFromToken(authToken);
                        String role = jwtUtils.getRoleFromToken(authToken);

                        // 创建认证对象，确保角色有ROLE_前缀
                        String authorityName = role.startsWith("ROLE_") ? role : "ROLE_" + role;
                        SimpleGrantedAuthority authority = new SimpleGrantedAuthority(authorityName);

                        // 将用户信息添加到认证对象中
                        UserPrincipal userPrincipal = new UserPrincipal(userId, username, role);
                        UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(
                            userPrincipal, null, Collections.singletonList(authority));
                        authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));

                        // 设置到安全上下文
                        SecurityContextHolder.getContext().setAuthentication(authentication);

                        log.debug("用户 {} 认证成功，角色: {}", username, role);
                    }
                }
            } catch (Exception e) {
                log.warn("JWT Token验证失败: {}", e.getMessage());
            }
        }
        
        filterChain.doFilter(request, response);
    }



    /**
     * 判断是否跳过认证
     */
    private boolean shouldSkipAuthentication(String requestURI) {
        log.debug("检查请求路径是否需要认证: {}", requestURI);

        // 公共API路径
        if (requestURI.startsWith("/api/auth/") ||
            requestURI.startsWith("/api/test/") ||
            requestURI.startsWith("/public/") ||
            requestURI.startsWith("/websocket/") ||
            requestURI.startsWith("/stats/public/")) {
            return true;
        }

        // Swagger文档路径
        if (requestURI.startsWith("/swagger-ui/") ||
            requestURI.startsWith("/v3/api-docs/") ||
            requestURI.startsWith("/swagger-resources/") ||
            requestURI.startsWith("/webjars/") ||
            requestURI.equals("/doc.html") ||
            requestURI.equals("/swagger-ui.html") ||
            requestURI.equals("/favicon.ico")) {
            return true;
        }

        // 静态资源路径
        if (requestURI.startsWith("/css/") ||
            requestURI.startsWith("/js/") ||
            requestURI.startsWith("/images/")) {
            return true;
        }

        return false;
    }

    /**
     * 用户主体信息
     */
    @Getter
    public static class UserPrincipal {
        private final Long userId;
        private final String username;
        private final String role;

        public UserPrincipal(Long userId, String username, String role) {
            this.userId = userId;
            this.username = username;
            this.role = role;
        }

    }
}
